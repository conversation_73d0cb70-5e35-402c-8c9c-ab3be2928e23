# Cell 5: Complete GA and PSO Variants + Hybrid Combinations

# Additional GA Variants
class AdaptiveGA(BaseMotifDiscovery):
    """Genetic Algorithm with adaptive mutation rate"""
    
    def discover_motifs(self) -> MotifResult:
        start_time = time.time()
        population_size = self.config.get('population_size', 20)
        max_generations = self.config.get('max_generations', 30)
        initial_mutation_rate = self.config.get('mutation_rate', 0.1)
        convergence_history = []
        
        # Initialize population
        population = self._initialize_population(population_size)
        best_candidate = max(population, key=lambda x: x.fitness)
        
        for generation in range(max_generations):
            # Adaptive mutation rate (decreases over time)
            mutation_rate = initial_mutation_rate * (1 - generation / max_generations)
            
            # Evaluate fitness
            for candidate in population:
                candidate.fitness = self._evaluate_fitness(candidate)
            
            # Track best
            current_best = max(population, key=lambda x: x.fitness)
            if current_best.fitness > best_candidate.fitness:
                best_candidate = copy.deepcopy(current_best)
            
            convergence_history.append(best_candidate.fitness)
            
            # Selection, crossover, mutation
            new_population = []
            for _ in range(population_size):
                parent1 = self._tournament_selection(population)
                parent2 = self._tournament_selection(population)
                child = self._crossover(parent1, parent2)
                child = self._mutate(child, mutation_rate)
                new_population.append(child)
            
            population = new_population
        
        # Create result
        motif = self._extract_motif(best_candidate)
        pwm = self.calculate_pwm([motif] if motif else [])
        ic = self.calculate_information_content(pwm)
        runtime = time.time() - start_time
        
        return MotifResult(
            motif=motif,
            score=best_candidate.fitness,
            positions=best_candidate.positions,
            pwm=pwm,
            information_content=ic,
            algorithm="Adaptive_GA",
            runtime=runtime,
            convergence_history=convergence_history
        )

class MemeticGA(BaseMotifDiscovery):
    """Genetic Algorithm with local search (Memetic Algorithm)"""
    
    def discover_motifs(self) -> MotifResult:
        start_time = time.time()
        population_size = self.config.get('population_size', 20)
        max_generations = self.config.get('max_generations', 30)
        mutation_rate = self.config.get('mutation_rate', 0.1)
        local_search_prob = self.config.get('local_search_prob', 0.3)
        convergence_history = []
        
        # Initialize population
        population = self._initialize_population(population_size)
        best_candidate = max(population, key=lambda x: x.fitness)
        
        for generation in range(max_generations):
            # Evaluate fitness
            for candidate in population:
                candidate.fitness = self._evaluate_fitness(candidate)
                
                # Apply local search with probability
                if random.random() < local_search_prob:
                    candidate = self._local_search(candidate)
            
            # Track best
            current_best = max(population, key=lambda x: x.fitness)
            if current_best.fitness > best_candidate.fitness:
                best_candidate = copy.deepcopy(current_best)
            
            convergence_history.append(best_candidate.fitness)
            
            # Selection, crossover, mutation
            new_population = []
            for _ in range(population_size):
                parent1 = self._tournament_selection(population)
                parent2 = self._tournament_selection(population)
                child = self._crossover(parent1, parent2)
                child = self._mutate(child, mutation_rate)
                new_population.append(child)
            
            population = new_population
        
        # Create result
        motif = self._extract_motif(best_candidate)
        pwm = self.calculate_pwm([motif] if motif else [])
        ic = self.calculate_information_content(pwm)
        runtime = time.time() - start_time
        
        return MotifResult(
            motif=motif,
            score=best_candidate.fitness,
            positions=best_candidate.positions,
            pwm=pwm,
            information_content=ic,
            algorithm="Memetic_GA",
            runtime=runtime,
            convergence_history=convergence_history
        )
    
    def _local_search(self, candidate):
        """Simple local search: try small position adjustments"""
        best_candidate = copy.deepcopy(candidate)
        best_fitness = self._evaluate_fitness(best_candidate)
        
        for i in range(len(candidate.positions)):
            for delta in [-1, 1]:  # Try moving position left or right
                new_candidate = copy.deepcopy(candidate)
                new_pos = candidate.positions[i] + delta
                
                # Check bounds
                if 0 <= new_pos <= len(self.sequences[i]) - self.motif_length:
                    new_candidate.positions[i] = new_pos
                    fitness = self._evaluate_fitness(new_candidate)
                    
                    if fitness > best_fitness:
                        best_candidate = new_candidate
                        best_fitness = fitness
        
        return best_candidate

# Additional PSO Variants
class ConstrictionPSO(BaseMotifDiscovery):
    """PSO with constriction factor"""
    
    def discover_motifs(self) -> MotifResult:
        start_time = time.time()
        swarm_size = self.config.get('swarm_size', 15)
        max_iterations = self.config.get('max_generations', 30)
        c1 = self.config.get('c1', 2.05)
        c2 = self.config.get('c2', 2.05)
        
        # Constriction factor
        phi = c1 + c2
        chi = 2 / abs(2 - phi - math.sqrt(phi**2 - 4*phi)) if phi > 4 else 0.729
        
        convergence_history = []
        
        # Initialize swarm
        swarm = self._initialize_swarm(swarm_size)
        global_best = self._get_global_best(swarm)
        
        for iteration in range(max_iterations):
            for particle in swarm:
                # Update velocity with constriction
                for i in range(len(particle.positions)):
                    r1, r2 = random.random(), random.random()
                    
                    cognitive = c1 * r1 * (particle.best_positions[i] - particle.positions[i])
                    social = c2 * r2 * (global_best.best_positions[i] - particle.positions[i])
                    
                    particle.velocities[i] = chi * (particle.velocities[i] + cognitive + social)
                    
                    # Update position
                    new_pos = int(particle.positions[i] + particle.velocities[i])
                    max_pos = max(0, len(self.sequences[i]) - self.motif_length)
                    particle.positions[i] = max(0, min(max_pos, new_pos))
                
                # Evaluate and update personal best
                particle.current_fitness = self._evaluate_particle_fitness(particle)
                if particle.current_fitness > particle.best_fitness:
                    particle.best_fitness = particle.current_fitness
                    particle.best_positions = particle.positions.copy()
            
            # Update global best
            current_global_best = self._get_global_best(swarm)
            if current_global_best.best_fitness > global_best.best_fitness:
                global_best = copy.deepcopy(current_global_best)
            
            convergence_history.append(global_best.best_fitness)
        
        # Create result
        motif = self._extract_motif_from_positions(global_best.best_positions)
        pwm = self.calculate_pwm([motif] if motif else [])
        ic = self.calculate_information_content(pwm)
        runtime = time.time() - start_time
        
        return MotifResult(
            motif=motif,
            score=global_best.best_fitness,
            positions=global_best.best_positions,
            pwm=pwm,
            information_content=ic,
            algorithm="Constriction_PSO",
            runtime=runtime,
            convergence_history=convergence_history
        )

class BareBonesPSO(BaseMotifDiscovery):
    """Bare-bones PSO using Gaussian distribution"""
    
    def discover_motifs(self) -> MotifResult:
        start_time = time.time()
        swarm_size = self.config.get('swarm_size', 15)
        max_iterations = self.config.get('max_generations', 30)
        convergence_history = []
        
        # Initialize swarm (no velocities needed)
        swarm = []
        for _ in range(swarm_size):
            particle = Particle(
                positions=[random.randint(0, max(0, len(seq) - self.motif_length)) 
                          for seq in self.sequences],
                velocities=[]  # Not used in bare-bones PSO
            )
            particle.current_fitness = self._evaluate_particle_fitness(particle)
            particle.best_fitness = particle.current_fitness
            particle.best_positions = particle.positions.copy()
            swarm.append(particle)
        
        global_best = max(swarm, key=lambda p: p.best_fitness)
        
        for iteration in range(max_iterations):
            for particle in swarm:
                # Update positions using Gaussian distribution
                for i in range(len(particle.positions)):
                    mean = (particle.best_positions[i] + global_best.best_positions[i]) / 2
                    std = abs(particle.best_positions[i] - global_best.best_positions[i])
                    
                    if std == 0:
                        std = 1  # Avoid zero standard deviation
                    
                    # Sample new position
                    new_pos = int(np.random.normal(mean, std))
                    
                    # Apply bounds
                    max_pos = max(0, len(self.sequences[i]) - self.motif_length)
                    particle.positions[i] = max(0, min(max_pos, new_pos))
                
                # Evaluate and update personal best
                particle.current_fitness = self._evaluate_particle_fitness(particle)
                if particle.current_fitness > particle.best_fitness:
                    particle.best_fitness = particle.current_fitness
                    particle.best_positions = particle.positions.copy()
            
            # Update global best
            current_best = max(swarm, key=lambda p: p.best_fitness)
            if current_best.best_fitness > global_best.best_fitness:
                global_best = copy.deepcopy(current_best)
            
            convergence_history.append(global_best.best_fitness)
        
        # Create result
        motif = self._extract_motif_from_positions(global_best.best_positions)
        pwm = self.calculate_pwm([motif] if motif else [])
        ic = self.calculate_information_content(pwm)
        runtime = time.time() - start_time
        
        return MotifResult(
            motif=motif,
            score=global_best.best_fitness,
            positions=global_best.best_positions,
            pwm=pwm,
            information_content=ic,
            algorithm="BareBones_PSO",
            runtime=runtime,
            convergence_history=convergence_history
        )

# Hybrid GA+PSO Combinations
class HybridGAPSO(BaseMotifDiscovery):
    """Base class for GA+PSO hybrid algorithms"""

    def __init__(self, sequences, motif_length, config, ga_class, pso_class):
        super().__init__(sequences, motif_length, config)
        self.ga_class = ga_class
        self.pso_class = pso_class
        self.hybrid_name = f"{ga_class.__name__}+{pso_class.__name__}"

    def discover_motifs(self) -> MotifResult:
        start_time = time.time()
        convergence_history = []

        # Phase 1: GA exploration (60% of iterations)
        ga_iterations = int(self.config.get('max_generations', 30) * 0.6)
        ga_config = self.config.copy()
        ga_config['max_generations'] = ga_iterations

        print(f"   🧬 Phase 1: {self.ga_class.__name__} exploration ({ga_iterations} iterations)")
        ga_algorithm = self.ga_class(self.sequences, self.motif_length, ga_config)
        ga_result = ga_algorithm.discover_motifs()

        # Phase 2: PSO refinement (40% of iterations)
        pso_iterations = int(self.config.get('max_generations', 30) * 0.4)
        pso_config = self.config.copy()
        pso_config['max_generations'] = pso_iterations

        print(f"   🔄 Phase 2: {self.pso_class.__name__} refinement ({pso_iterations} iterations)")
        pso_algorithm = self.pso_class(self.sequences, self.motif_length, pso_config)
        pso_result = pso_algorithm.discover_motifs()

        # Combine convergence histories
        convergence_history.extend(ga_result.convergence_history)
        convergence_history.extend(pso_result.convergence_history)

        # Select best result
        if pso_result.score >= ga_result.score:
            best_result = pso_result
            print(f"   🎯 PSO phase achieved better result: {pso_result.score:.4f}")
        else:
            best_result = ga_result
            print(f"   🎯 GA phase achieved better result: {ga_result.score:.4f}")

        runtime = time.time() - start_time

        return MotifResult(
            motif=best_result.motif,
            score=best_result.score,
            positions=best_result.positions,
            pwm=best_result.pwm,
            information_content=best_result.information_content,
            algorithm=self.hybrid_name,
            runtime=runtime,
            convergence_history=convergence_history
        )

def test_all_hybrid_combinations(sequences, labels, config):
    """Test all 9 GA+PSO hybrid combinations"""

    # Define GA and PSO variants
    ga_variants = [ElitistGA, AdaptiveGA, MemeticGA]
    pso_variants = [InertiaWeightPSO, ConstrictionPSO, BareBonesPSO]

    print("🚀 Testing All 9 Hybrid GA+PSO Combinations")
    print("="*60)

    results = {}

    for i, ga_class in enumerate(ga_variants):
        for j, pso_class in enumerate(pso_variants):
            combination_name = f"{ga_class.__name__}+{pso_class.__name__}"
            print(f"\n🔬 Testing Combination {i*3+j+1}/9: {combination_name}")
            print("-" * 50)

            # Create hybrid algorithm
            hybrid = HybridGAPSO(sequences, config['motif_length'], config, ga_class, pso_class)

            # Run the hybrid algorithm
            result = hybrid.discover_motifs()

            # Store results
            results[combination_name] = result

            # Display results
            print(f"✅ {combination_name} Results:")
            print(f"   🧬 Motif: {result.motif}")
            print(f"   📈 Score: {result.score:.4f}")
            print(f"   ⏱️ Runtime: {result.runtime:.2f}s")
            print(f"   📊 Information Content: {result.information_content:.4f}")

    return results

def visualize_hybrid_results(results):
    """Visualize all hybrid combination results"""

    # Extract data for visualization
    names = list(results.keys())
    scores = [result.score for result in results.values()]
    runtimes = [result.runtime for result in results.values()]
    motifs = [result.motif for result in results.values()]

    # Create comprehensive visualization
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 1. Score comparison
    axes[0, 0].bar(range(len(names)), scores, color='skyblue', alpha=0.8)
    axes[0, 0].set_title('Hybrid Algorithm Scores')
    axes[0, 0].set_xlabel('Algorithm')
    axes[0, 0].set_ylabel('Score')
    axes[0, 0].set_xticks(range(len(names)))
    axes[0, 0].set_xticklabels(names, rotation=45, ha='right')

    # Add score values on bars
    for i, score in enumerate(scores):
        axes[0, 0].text(i, score + 0.01, f'{score:.3f}', ha='center', va='bottom')

    # 2. Runtime comparison
    axes[0, 1].bar(range(len(names)), runtimes, color='lightcoral', alpha=0.8)
    axes[0, 1].set_title('Hybrid Algorithm Runtimes')
    axes[0, 1].set_xlabel('Algorithm')
    axes[0, 1].set_ylabel('Runtime (seconds)')
    axes[0, 1].set_xticks(range(len(names)))
    axes[0, 1].set_xticklabels(names, rotation=45, ha='right')

    # 3. Score vs Runtime scatter
    axes[1, 0].scatter(runtimes, scores, c=range(len(names)), cmap='viridis', s=100, alpha=0.7)
    axes[1, 0].set_xlabel('Runtime (seconds)')
    axes[1, 0].set_ylabel('Score')
    axes[1, 0].set_title('Score vs Runtime Trade-off')

    # Add labels to points
    for i, name in enumerate(names):
        axes[1, 0].annotate(name.split('+')[0][:3] + '+' + name.split('+')[1][:3],
                           (runtimes[i], scores[i]), xytext=(5, 5),
                           textcoords='offset points', fontsize=8)

    # 4. Motif diversity heatmap
    motif_matrix = np.zeros((len(motifs), len(motifs)))
    for i, motif1 in enumerate(motifs):
        for j, motif2 in enumerate(motifs):
            if motif1 and motif2 and len(motif1) == len(motif2):
                similarity = sum(a == b for a, b in zip(motif1, motif2)) / len(motif1)
                motif_matrix[i, j] = similarity

    im = axes[1, 1].imshow(motif_matrix, cmap='Blues', aspect='auto')
    axes[1, 1].set_title('Motif Similarity Matrix')
    axes[1, 1].set_xticks(range(len(names)))
    axes[1, 1].set_yticks(range(len(names)))
    axes[1, 1].set_xticklabels([name.split('+')[0][:3] + '+' + name.split('+')[1][:3]
                               for name in names], rotation=45, ha='right')
    axes[1, 1].set_yticklabels([name.split('+')[0][:3] + '+' + name.split('+')[1][:3]
                               for name in names])

    # Add colorbar
    plt.colorbar(im, ax=axes[1, 1], label='Similarity')

    plt.tight_layout()
    plt.show()

    # Summary statistics
    print(f"\n📊 Hybrid Algorithm Summary:")
    print(f"   🏆 Best Score: {max(scores):.4f} ({names[scores.index(max(scores))]})")
    print(f"   ⚡ Fastest: {min(runtimes):.2f}s ({names[runtimes.index(min(runtimes))]})")
    print(f"   📈 Average Score: {np.mean(scores):.4f}")
    print(f"   ⏱️ Average Runtime: {np.mean(runtimes):.2f}s")

    return names[scores.index(max(scores))]  # Return best algorithm name

print("✅ All GA and PSO variants implemented!")
print("🎯 Hybrid combinations ready!")
print("🚀 Ready to test all 9 combinations!")
