# Clean Architecture for Alzheimer's DNA Motif Discovery

## Professional Architecture Diagrams - No Emojis, High Visibility

---

## 1. Main Architecture Diagram (Detailed)

```mermaid
flowchart TD
    %% Input Layer
    A[Input DNA Sequences<br/>APP, PSEN1, PSEN2 genes] --> B[Data Preprocessing<br/>Validation & Segmentation]
    
    %% Algorithm Modules
    B --> C1[Genetic Algorithm Module<br/>- Elitist GA<br/>- Adaptive GA<br/>- Memetic GA]
    
    B --> C2[PSO Module<br/>- Inertia Weight PSO<br/>- Constriction Coefficient PSO<br/>- Bare-Bones PSO]
    
    %% Novel Hybrid System
    C1 --> D[Novel Hybrid GA+PSO Engine<br/>Systematic Combination Framework]
    C2 --> D
    
    %% 9 Hybrid Combinations
    D --> E[9 Hybrid Combinations<br/>All GA x PSO variants]
    
    subgraph combinations [" "]
        E1[Elitist + Inertia Weight]
        E2[Elitist + Constriction]
        E3[Elitist + Bare-bones]
        E4[Adaptive + Inertia Weight]
        E5[Adaptive + Constriction]
        E6[Adaptive + Bare-bones]
        E7[Memetic + Inertia Weight]
        E8[Memetic + Constriction]
        E9[Memetic + Bare-bones]
    end
    
    E --> E1
    E --> E2
    E --> E3
    E --> E4
    E --> E5
    E --> E6
    E --> E7
    E --> E8
    E --> E9
    
    %% Performance Evaluation
    E1 --> F[Performance Evaluation Engine<br/>Multi-run statistical analysis<br/>Robustness assessment<br/>Best combination selection]
    E2 --> F
    E3 --> F
    E4 --> F
    E5 --> F
    E6 --> F
    E7 --> F
    E8 --> F
    E9 --> F
    
    %% Motif Analysis
    F --> G[Motif Analysis Pipeline<br/>PWM Generation<br/>Information Content Calculation<br/>Conservation Scoring<br/>Statistical Significance]
    
    %% Explainability
    G --> H[Explainability Engine<br/>Algorithm Performance Analysis<br/>Convergence Pattern Analysis<br/>Diversity Metrics<br/>Biological Interpretation]
    
    %% ML Validation
    G --> I[ML Validation Module<br/>Feature Engineering<br/>Model Training<br/>Cross-validation<br/>Performance Metrics]
    
    %% Output
    H --> J[Validated Alzheimer's Motifs<br/>High-quality motifs<br/>Biological significance<br/>Predictive power<br/>Clinical relevance]
    I --> J
    
    %% Clean Styling
    classDef input fill:#E3F2FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef algorithm fill:#F3E5F5,stroke:#7B1FA2,stroke-width:2px,color:#000
    classDef hybrid fill:#FFEBEE,stroke:#D32F2F,stroke-width:3px,color:#000
    classDef combinations fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef evaluation fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef analysis fill:#FCE4EC,stroke:#C2185B,stroke-width:2px,color:#000
    classDef ml fill:#F1F8E9,stroke:#689F38,stroke-width:2px,color:#000
    classDef output fill:#FFF8E1,stroke:#FFA000,stroke-width:3px,color:#000
    
    class A,B input
    class C1,C2 algorithm
    class D,E hybrid
    class E1,E2,E3,E4,E5,E6,E7,E8,E9 combinations
    class F,G evaluation
    class H analysis
    class I ml
    class J output
```

---

## 2. Simplified Architecture (Publication Ready)

```mermaid
flowchart TD
    %% Input
    A[Input DNA Sequences<br/>APP, PSEN1, PSEN2] --> B[Data Preprocessing]
    
    %% Algorithm Components
    B --> C[Genetic Algorithm Variants<br/>Elitist, Adaptive, Memetic]
    B --> D[PSO Variants<br/>Inertia Weight, Constriction, Bare-bones]
    
    %% Novel Hybrid System
    C --> E[Novel Hybrid System<br/>9 GA x PSO Combinations]
    D --> E
    
    %% Performance Evaluation
    E --> F[Performance Evaluation<br/>Statistical Analysis & Selection]
    
    %% Analysis Pipeline
    F --> G[Motif Analysis<br/>PWM Generation & Scoring]
    
    %% Validation & Explainability
    G --> H[ML Validation<br/>Classification Models]
    G --> I[Explainability Analysis<br/>Performance & Biological Insights]
    
    %% Final Output
    H --> J[Validated Alzheimer's Motifs<br/>Clinical Relevance]
    I --> J
    
    %% Professional Styling
    classDef input fill:#E8F4FD,stroke:#1565C0,stroke-width:2px,color:#000,font-size:12px
    classDef algorithm fill:#F3E5F5,stroke:#7B1FA2,stroke-width:2px,color:#000,font-size:12px
    classDef hybrid fill:#FFEBEE,stroke:#C62828,stroke-width:3px,color:#000,font-size:12px
    classDef evaluation fill:#E8F5E8,stroke:#2E7D32,stroke-width:2px,color:#000,font-size:12px
    classDef analysis fill:#FFF3E0,stroke:#EF6C00,stroke-width:2px,color:#000,font-size:12px
    classDef output fill:#F1F8E9,stroke:#558B2F,stroke-width:3px,color:#000,font-size:12px
    
    class A,B input
    class C,D algorithm
    class E hybrid
    class F,G evaluation
    class H,I analysis
    class J output
```

---

## 3. 9 Hybrid Combinations Matrix

```mermaid
graph TB
    subgraph "Novel Hybrid GA+PSO System"
        INPUT[Preprocessed DNA Sequences] --> SYSTEM[Comprehensive Hybrid Evaluation]
        
        subgraph "Elitist GA Combinations"
            H1[Elitist + Inertia Weight<br/>3 runs x 50 generations]
            H2[Elitist + Constriction<br/>3 runs x 50 generations]
            H3[Elitist + Bare-bones<br/>3 runs x 50 generations]
        end
        
        subgraph "Adaptive GA Combinations"
            H4[Adaptive + Inertia Weight<br/>3 runs x 50 generations]
            H5[Adaptive + Constriction<br/>3 runs x 50 generations]
            H6[Adaptive + Bare-bones<br/>3 runs x 50 generations]
        end
        
        subgraph "Memetic GA Combinations"
            H7[Memetic + Inertia Weight<br/>3 runs x 50 generations]
            H8[Memetic + Constriction<br/>3 runs x 50 generations]
            H9[Memetic + Bare-bones<br/>3 runs x 50 generations]
        end
        
        SYSTEM --> H1
        SYSTEM --> H2
        SYSTEM --> H3
        SYSTEM --> H4
        SYSTEM --> H5
        SYSTEM --> H6
        SYSTEM --> H7
        SYSTEM --> H8
        SYSTEM --> H9
        
        H1 --> EVAL[Statistical Evaluation<br/>Performance ranking<br/>Robustness analysis<br/>Statistical significance]
        H2 --> EVAL
        H3 --> EVAL
        H4 --> EVAL
        H5 --> EVAL
        H6 --> EVAL
        H7 --> EVAL
        H8 --> EVAL
        H9 --> EVAL
        
        EVAL --> WINNER[Best Hybrid Selection<br/>Automatic winner selection<br/>Performance-based ranking]
    end
    
    subgraph "Performance Metrics"
        METRICS[Evaluation Criteria<br/>Fitness score<br/>Convergence rate<br/>Solution quality<br/>Robustness<br/>Statistical significance]
        
        COMPARISON[Baseline Comparison<br/>vs Traditional methods<br/>vs Deep Learning<br/>vs Individual algorithms<br/>Superiority validation]
    end
    
    WINNER --> METRICS
    WINNER --> COMPARISON
    
    %% Clean Styling
    classDef input fill:#E3F2FD,stroke:#1976D2,stroke-width:2px,color:#000
    classDef elitist fill:#FFEBEE,stroke:#D32F2F,stroke-width:2px,color:#000
    classDef adaptive fill:#E8F5E8,stroke:#388E3C,stroke-width:2px,color:#000
    classDef memetic fill:#FFF3E0,stroke:#F57C00,stroke-width:2px,color:#000
    classDef evaluation fill:#F3E5F5,stroke:#7B1FA2,stroke-width:2px,color:#000
    classDef winner fill:#FFF8E1,stroke:#FFA000,stroke-width:3px,color:#000
    classDef metrics fill:#F1F8E9,stroke:#689F38,stroke-width:2px,color:#000
    
    class INPUT,SYSTEM input
    class H1,H2,H3 elitist
    class H4,H5,H6 adaptive
    class H7,H8,H9 memetic
    class EVAL evaluation
    class WINNER winner
    class METRICS,COMPARISON metrics
```

---

## Usage Instructions

### How to Use These Diagrams:

1. **Copy the Mermaid code** from any section above
2. **Go to**: https://mermaid.live/
3. **Paste the code** to see the visual diagram
4. **Export as PNG/SVG** for your paper
5. **Adjust colors** if needed by modifying the classDef lines

### For Different Purposes:

- **Main Paper**: Use the Simplified Architecture (Section 2)
- **Technical Details**: Use the Detailed Architecture (Section 1)  
- **Methodology Section**: Use the 9 Combinations Matrix (Section 3)
- **Presentations**: Use the Simplified Architecture for clarity

### Key Improvements:

✅ **No Emojis**: Clean, professional appearance
✅ **High Contrast**: Better visibility and readability
✅ **Clear Labels**: Descriptive text without clutter
✅ **Professional Colors**: Suitable for academic publications
✅ **Scalable Design**: Works well in papers and presentations
✅ **Multiple Versions**: Choose the right level of detail

### Color Scheme:
- **Blue**: Input/Data layers
- **Purple**: Algorithm components  
- **Red**: Novel hybrid system (your contribution)
- **Green**: Evaluation and analysis
- **Orange**: Performance metrics
- **Yellow**: Final outputs

This clean architecture is now **publication-ready** and **highly visible**!
