{
  "nbformat": 4,
  "nbformat_minor": 0,
  "metadata": {
    "colab": {
      "provenance": []
    },
    "kernelspec": {
      "name": "python3",
      "display_name": "Python 3"
    },
    "language_info": {
      "name": "python"
    }
  },
  "cells": [
    {
      "cell_type": "code",
      "source": [
        "# Enhanced APP Gene Motif Discovery using GA and PSO\n",
        "# Optimized for Q1 Publication Quality\n",
        "\n",
        "import os\n",
        "import time\n",
        "import warnings\n",
        "import random\n",
        "import math\n",
        "from pathlib import Path\n",
        "from typing import Dict, List, Any, Tuple, Optional\n",
        "from abc import ABC, abstractmethod\n",
        "from dataclasses import dataclass\n",
        "\n",
        "import numpy as np\n",
        "import pandas as pd\n",
        "from scipy import stats\n",
        "import matplotlib.pyplot as plt\n",
        "import seaborn as sns\n",
        "from tqdm.notebook import tqdm\n",
        "\n",
        "# Configuration\n",
        "warnings.filterwarnings('ignore')\n",
        "plt.style.use('seaborn-v0_8')\n",
        "sns.set_palette(\"husl\")\n",
        "\n",
        "# Set random seeds for reproducibility\n",
        "RANDOM_SEED = 42\n",
        "random.seed(RANDOM_SEED)\n",
        "np.random.seed(RANDOM_SEED)\n",
        "\n",
        "# Dataset configuration - adapt path as needed\n",
        "dataset_path = './Dataset_from reference genome'  # Local path\n",
        "# dataset_path = '/content/drive/MyDrive/Dataset_from reference genome'  # Colab path\n",
        "\n",
        "print(\"🧬 Enhanced APP Gene Motif Discovery System\")\n",
        "print(\"📊 Optimized for Q1 Publication Quality\")\n",
        "print(f\"📁 Dataset path: {dataset_path}\")\n",
        "print(f\"📁 Dataset exists: {os.path.exists(dataset_path)}\")\n",
        "\n",
        "if os.path.exists(dataset_path):\n",
        "    files = [f for f in os.listdir(dataset_path) if f.endswith('.fasta')]\n",
        "    print(f\"📁 Found {len(files)} FASTA files\")\n",
        "    for file in files:\n",
        "        if 'app' in file.lower():\n",
        "            print(f\"   ✅ {file}\")\n",
        "else:\n",
        "    print(\"❌ Dataset folder not found! Please update the path.\")"\n",
        "\n",
        "# ============================================================================\n",
        "# CORE DATA STRUCTURES AND CONFIGURATION\n",
        "# ============================================================================\n",
        "\n",
        "@dataclass\n",
        "class MotifResult:\n",
        "    \"\"\"Enhanced motif discovery result with comprehensive metrics.\"\"\"\n",
        "    motif: str\n",
        "    score: float\n",
        "    positions: List[int]\n",
        "    pwm: np.ndarray\n",
        "    information_content: float\n",
        "    algorithm: str\n",
        "    runtime: float\n",
        "    convergence_history: List[float]\n",
        "    \n",
        "    # Enhanced metrics for publication\n",
        "    conservation_score: float = 0.0\n",
        "    statistical_significance: float = 1.0\n",
        "    motif_instances: List[str] = None\n",
        "    sequence_coverage: float = 0.0\n",
        "    \n",
        "    def __post_init__(self):\n",
        "        if self.motif_instances is None:\n",
        "            self.motif_instances = []\n",
        "    \n",
        "    def to_dict(self) -> Dict[str, Any]:\n",
        "        \"\"\"Convert to dictionary for analysis and export.\"\"\"\n",
        "        return {\n",
        "            'motif': self.motif,\n",
        "            'score': self.score,\n",
        "            'positions': self.positions,\n",
        "            'pwm': self.pwm.tolist() if self.pwm is not None else None,\n",
        "            'information_content': self.information_content,\n",
        "            'conservation_score': self.conservation_score,\n",
        "            'statistical_significance': self.statistical_significance,\n",
        "            'sequence_coverage': self.sequence_coverage,\n",
        "            'algorithm': self.algorithm,\n",
        "            'runtime': self.runtime,\n",
        "            'convergence_history': self.convergence_history,\n",
        "            'motif_instances': self.motif_instances\n",
        "        }\n",
        "\n",
        "@dataclass\n",
        "class OptimizationConfig:\n",
        "    \"\"\"Optimized configuration for GA and PSO algorithms.\"\"\"\n",
        "    # Common parameters\n",
        "    population_size: int = 50\n",
        "    max_generations: int = 100\n",
        "    convergence_threshold: float = 1e-6\n",
        "    max_stagnation: int = 15\n",
        "    \n",
        "    # GA-specific parameters\n",
        "    mutation_rate: float = 0.15\n",
        "    crossover_rate: float = 0.85\n",
        "    elitism_ratio: float = 0.1  # Proportion of population to keep as elite\n",
        "    tournament_size: int = 3\n",
        "    \n",
        "    # PSO-specific parameters\n",
        "    inertia_weight: float = 0.9\n",
        "    inertia_decay: float = 0.99  # Decay inertia over time\n",
        "    cognitive_coef: float = 2.0\n",
        "    social_coef: float = 2.0\n",
        "    velocity_clamp: float = 0.2  # Fraction of search space\n",
        "    \n",
        "    def get_elitism_size(self) -> int:\n",
        "        \"\"\"Calculate elite population size.\"\"\"\n",
        "        return max(1, int(self.population_size * self.elitism_ratio))\n",
        "\n",
        "# Base Abstract Class\n",
        "class BaseMotifDiscovery(ABC):\n",
        "    \"\"\"Abstract base class for all motif discovery algorithms\"\"\"\n",
        "\n",
        "    def __init__(self, sequences: List[str], motif_length: int, config: OptimizationConfig = None):\n",
        "        self.sequences = [seq.upper() for seq in sequences if seq]  # Ensure uppercase and non-empty\n",
        "        self.motif_length = motif_length\n",
        "        self.config = config or OptimizationConfig()\n",
        "        self.nucleotides = ['A', 'T', 'G', 'C']\n",
        "        self.nucleotide_to_index = {nuc: idx for idx, nuc in enumerate(self.nucleotides)}\n",
        "\n",
        "        # Validate inputs\n",
        "        if not self.sequences:\n",
        "            raise ValueError(\"No valid sequences provided\")\n",
        "        if motif_length <= 0:\n",
        "            raise ValueError(\"Motif length must be positive\")\n",
        "        if any(len(seq) < motif_length for seq in self.sequences):\n",
        "            print(f\"⚠️  Warning: Some sequences shorter than motif length ({motif_length})\")\n",
        "\n",
        "    @abstractmethod\n",
        "    def discover_motifs(self) -> MotifResult:\n",
        "        \"\"\"Abstract method to discover motifs - must be implemented by subclasses\"\"\"\n",
        "        pass\n",
        "\n",
        "    def calculate_pwm(self, motif_instances: List[str]) -> np.ndarray:\n",
        "        \"\"\"Calculate Position Weight Matrix from motif instances\"\"\"\n",
        "        if not motif_instances:\n",
        "            return np.ones((4, self.motif_length)) * 0.25\n",
        "\n",
        "        pwm = np.zeros((4, self.motif_length))\n",
        "        pseudocount = 0.001  # Small pseudocount to avoid log(0)\n",
        "\n",
        "        for pos in range(self.motif_length):\n",
        "            for motif in motif_instances:\n",
        "                if pos < len(motif) and motif[pos] in self.nucleotide_to_index:\n",
        "                    pwm[self.nucleotide_to_index[motif[pos]], pos] += 1\n",
        "\n",
        "            # Add pseudocounts and normalize\n",
        "            pwm[:, pos] += pseudocount\n",
        "            pwm[:, pos] /= np.sum(pwm[:, pos])\n",
        "\n",
        "        return pwm\n",
        "\n",
        "    def calculate_information_content(self, pwm: np.ndarray) -> float:\n",
        "        \"\"\"Calculate information content of PWM\"\"\"\n",
        "        if pwm is None:\n",
        "            return 0.0\n",
        "\n",
        "        ic_total = 0.0\n",
        "        for pos in range(pwm.shape[1]):\n",
        "            ic_pos = 0.0\n",
        "            for nuc_prob in pwm[:, pos]:\n",
        "                if nuc_prob > 0:\n",
        "                    ic_pos += nuc_prob * np.log2(nuc_prob / 0.25)\n",
        "            ic_total += ic_pos\n",
        "\n",
        "        return ic_total\n",
        "\n",
        "    def score_motif(self, motif: str, sequences: List[str] = None) -> float:\n",
        "        \"\"\"Score a motif against sequences using information content and conservation\"\"\"\n",
        "        if sequences is None:\n",
        "            sequences = self.sequences\n",
        "\n",
        "        if not motif or len(motif) != self.motif_length:\n",
        "            return 0.0\n",
        "\n",
        "        # Find best matches in each sequence\n",
        "        motif_instances = []\n",
        "        total_matches = 0\n",
        "\n",
        "        for seq in sequences:\n",
        "            if len(seq) < self.motif_length:\n",
        "                continue\n",
        "\n",
        "            best_score = 0\n",
        "            best_match = \"\"\n",
        "\n",
        "            for i in range(len(seq) - self.motif_length + 1):\n",
        "                subseq = seq[i:i + self.motif_length]\n",
        "                match_score = self._sequence_similarity(motif, subseq)\n",
        "\n",
        "                if match_score > best_score:\n",
        "                    best_score = match_score\n",
        "                    best_match = subseq\n",
        "\n",
        "            if best_match:\n",
        "                motif_instances.append(best_match)\n",
        "                total_matches += best_score\n",
        "\n",
        "        if not motif_instances:\n",
        "            return 0.0\n",
        "\n",
        "        # Calculate PWM and information content\n",
        "        pwm = self.calculate_pwm(motif_instances)\n",
        "        ic = self.calculate_information_content(pwm)\n",
        "\n",
        "        # Combined score: information content weighted by average similarity\n",
        "        avg_similarity = total_matches / len(sequences)\n",
        "        combined_score = ic * avg_similarity\n",
        "\n",
        "        return combined_score\n",
        "\n",
        "    def _sequence_similarity(self, seq1: str, seq2: str) -> float:\n",
        "        \"\"\"Calculate similarity between two sequences\"\"\"\n",
        "        if len(seq1) != len(seq2):\n",
        "            return 0.0\n",
        "\n",
        "        matches = sum(1 for a, b in zip(seq1, seq2) if a == b)\n",
        "        return matches / len(seq1)\n",
        "\n",
        "    def _create_dummy_result(self, algorithm_name: str) -> MotifResult:\n",
        "        \"\"\"Create a dummy result when algorithm fails\"\"\"\n",
        "        dummy_motif = \"A\" * self.motif_length\n",
        "        dummy_pwm = np.ones((4, self.motif_length)) * 0.25\n",
        "\n",
        "        return MotifResult(\n",
        "            motif=dummy_motif,\n",
        "            score=0.0,\n",
        "            positions=[0] * len(self.sequences),\n",
        "            pwm=dummy_pwm,\n",
        "            information_content=0.0,\n",
        "            algorithm=algorithm_name,\n",
        "            runtime=0.0,\n",
        "            convergence_history=[0.0],\n",
        "            p_value=1.0,\n",
        "            conservation_score=0.0\n",
        "        )\n",
        "\n",
        "print(\"✅ Core data structures and base class defined!\")\n",
        "\n",
        "# Data Loading Functions\n",
        "def load_fasta_file(file_path: Path) -> List[str]:\n",
        "    \"\"\"Load sequences from FASTA file with proper header handling\"\"\"\n",
        "    sequences = []\n",
        "    try:\n",
        "        print(f\"📖 Reading file: {file_path}\")\n",
        "        current_seq = \"\"\n",
        "\n",
        "        with open(file_path, 'r') as f:\n",
        "            for line in f:\n",
        "                line = line.strip()\n",
        "                if line.startswith('>'):\n",
        "                    # Save previous sequence if exists\n",
        "                    if current_seq:\n",
        "                        sequences.append(current_seq.upper())\n",
        "                        current_seq = \"\"\n",
        "                    # Print header info (first few characters)\n",
        "                    print(f\"   Header: {line[:80]}...\")\n",
        "                else:\n",
        "                    # Accumulate sequence data\n",
        "                    current_seq += line\n",
        "\n",
        "            # Don't forget the last sequence\n",
        "            if current_seq:\n",
        "                sequences.append(current_seq.upper())\n",
        "\n",
        "        print(f\"✅ Successfully loaded {len(sequences)} sequences from {file_path.name}\")\n",
        "        if sequences:\n",
        "            print(f\"   Sample lengths: {[len(s) for s in sequences[:3]]}\")\n",
        "            print(f\"   Sample sequence: {sequences[0][:50]}...\")\n",
        "\n",
        "    except Exception as e:\n",
        "        print(f\"❌ Error loading FASTA file {file_path}: {e}\")\n",
        "\n",
        "    return sequences\n",
        "\n",
        "def load_app_genomic_data(dataset_path: str, max_sequences: int = 100) -> Tuple[List[str], List[int]]:\n",
        "    \"\"\"Load APP genomic data (healthy and unhealthy)\"\"\"\n",
        "    print(\"🧬 Loading APP genomic data...\")\n",
        "\n",
        "    sequences = []\n",
        "    labels = []\n",
        "\n",
        "    # Focus on APP files only\n",
        "    app_files = [\n",
        "        ('APP_healthy_combined.fasta', 0),\n",
        "        ('app_unhealthy_combined.fasta', 1)\n",
        "    ]\n",
        "\n",
        "    print(f\"🔍 Looking for APP files in: {dataset_path}\")\n",
        "\n",
        "    for file_name, label in app_files:\n",
        "        file_path = Path(dataset_path) / file_name\n",
        "        print(f\"   Checking: {file_name} - Exists: {file_path.exists()}\")\n",
        "\n",
        "        if file_path.exists():\n",
        "            file_sequences = load_fasta_file(file_path)\n",
        "            sequences.extend(file_sequences)\n",
        "            labels.extend([label] * len(file_sequences))\n",
        "            print(f\"✅ Loaded {len(file_sequences)} sequences from {file_name}\")\n",
        "        else:\n",
        "            print(f\"❌ File not found: {file_name}\")\n",
        "\n",
        "    print(f\"\\n🔍 Raw APP data loaded: {len(sequences)} sequences\")\n",
        "    if sequences:\n",
        "        print(f\"   Length range: {min(len(s) for s in sequences)} - {max(len(s) for s in sequences)}\")\n",
        "        print(f\"   Sample characters: {set(''.join(sequences[:3])[:100])}\")\n",
        "\n",
        "    # Process sequences - create smaller segments for motif discovery\n",
        "    processed_sequences = []\n",
        "    processed_labels = []\n",
        "    segment_length = 1000  # Create 1000bp segments for analysis\n",
        "\n",
        "    print(f\"🧬 Processing APP sequences into {segment_length}bp segments...\")\n",
        "\n",
        "    for seq, label in zip(sequences, labels):\n",
        "        # Skip very short sequences\n",
        "        if len(seq) < 100:\n",
        "            continue\n",
        "\n",
        "        # Create overlapping segments from longer sequences\n",
        "        if len(seq) > segment_length:\n",
        "            step_size = segment_length // 2  # 50% overlap\n",
        "            for start in range(0, len(seq) - segment_length + 1, step_size):\n",
        "                segment = seq[start:start + segment_length]\n",
        "\n",
        "                # Quality check for segment\n",
        "                valid_chars = set('ATGCN')\n",
        "                if len(set(segment) - valid_chars) == 0:  # Only valid nucleotides\n",
        "                    if segment.count('N') / len(segment) < 0.3:  # Less than 30% N's\n",
        "                        processed_sequences.append(segment)\n",
        "                        processed_labels.append(label)\n",
        "\n",
        "                        if len(processed_sequences) >= max_sequences:\n",
        "                            break\n",
        "\n",
        "                if len(processed_sequences) >= max_sequences:\n",
        "                    break\n",
        "        else:\n",
        "            # Use entire sequence if it's shorter than segment_length\n",
        "            valid_chars = set('ATGCN')\n",
        "            if len(set(seq) - valid_chars) == 0:\n",
        "                if seq.count('N') / len(seq) < 0.3:\n",
        "                    processed_sequences.append(seq)\n",
        "                    processed_labels.append(label)\n",
        "\n",
        "        if len(processed_sequences) >= max_sequences:\n",
        "            break\n",
        "\n",
        "    print(f\"\\n📊 APP data processing results:\")\n",
        "    print(f\"   Final sequences: {len(processed_sequences)}\")\n",
        "    if processed_sequences:\n",
        "        print(f\"   Healthy sequences (0): {sum(1 for l in processed_labels if l == 0)}\")\n",
        "        print(f\"   Unhealthy sequences (1): {sum(1 for l in processed_labels if l == 1)}\")\n",
        "        print(f\"   Average length: {np.mean([len(s) for s in processed_sequences]):.1f}\")\n",
        "        print(f\"   Length range: {min(len(s) for s in processed_sequences)} - {max(len(s) for s in processed_sequences)}\")\n",
        "\n",
        "    return processed_sequences, processed_labels\n",
        "\n",
        "def analyze_sequence_composition(sequences: List[str], labels: List[int]):\n",
        "    \"\"\"Analyze nucleotide composition of sequences\"\"\"\n",
        "    print(\"\\n🔬 Analyzing APP sequence composition...\")\n",
        "\n",
        "    healthy_seqs = [sequences[i] for i, l in enumerate(labels) if l == 0]\n",
        "    unhealthy_seqs = [sequences[i] for i, l in enumerate(labels) if l == 1]\n",
        "\n",
        "    def get_composition(seqs):\n",
        "        total_length = sum(len(s) for s in seqs)\n",
        "        if total_length == 0:\n",
        "            return {'A': 0, 'T': 0, 'G': 0, 'C': 0, 'N': 0}\n",
        "\n",
        "        counts = {'A': 0, 'T': 0, 'G': 0, 'C': 0, 'N': 0}\n",
        "        for seq in seqs:\n",
        "            for nuc in seq:\n",
        "                if nuc in counts:\n",
        "                    counts[nuc] += 1\n",
        "\n",
        "        # Convert to percentages\n",
        "        return {nuc: (count/total_length)*100 for nuc, count in counts.items()}\n",
        "\n",
        "    if healthy_seqs:\n",
        "        healthy_comp = get_composition(healthy_seqs)\n",
        "        print(f\"   Healthy APP composition: {healthy_comp}\")\n",
        "\n",
        "    if unhealthy_seqs:\n",
        "        unhealthy_comp = get_composition(unhealthy_seqs)\n",
        "        print(f\"   Unhealthy APP composition: {unhealthy_comp}\")\n",
        "\n",
        "    # Visualize composition\n",
        "    if healthy_seqs and unhealthy_seqs:\n",
        "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n",
        "\n",
        "        # Healthy composition\n",
        "        nucleotides = ['A', 'T', 'G', 'C']\n",
        "        healthy_values = [healthy_comp[nuc] for nuc in nucleotides]\n",
        "        ax1.bar(nucleotides, healthy_values, color=['red', 'blue', 'green', 'orange'], alpha=0.7)\n",
        "        ax1.set_title('Healthy APP Sequences\\nNucleotide Composition')\n",
        "        ax1.set_ylabel('Percentage (%)')\n",
        "        ax1.set_ylim(0, max(max(healthy_values), max([unhealthy_comp[nuc] for nuc in nucleotides])) * 1.1)\n",
        "\n",
        "        # Unhealthy composition\n",
        "        unhealthy_values = [unhealthy_comp[nuc] for nuc in nucleotides]\n",
        "        ax2.bar(nucleotides, unhealthy_values, color=['red', 'blue', 'green', 'orange'], alpha=0.7)\n",
        "        ax2.set_title('Unhealthy APP Sequences\\nNucleotide Composition')\n",
        "        ax2.set_ylabel('Percentage (%)')\n",
        "        ax2.set_ylim(0, max(max(healthy_values), max(unhealthy_values)) * 1.1)\n",
        "\n",
        "        plt.tight_layout()\n",
        "        plt.show()\n",
        "\n",
        "print(\"✅ Data loading functions defined!\")"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/"
        },
        "id": "l8MpvVreRgHT",
        "outputId": "39173450-c1ae-454e-b69c-da3ebb45fd3f"
      },
      "execution_count": 1,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n",
            "✅ Drive mounted successfully!\n",
            "📁 Dataset path: /content/drive/MyDrive/Dataset_from reference genome\n",
            "📁 Dataset exists: True\n",
            "📁 Dataset contains 6 files:\n",
            "   - psen2_unhealthy_combined.fasta\n",
            "   - APP_healthy_combined.fasta\n",
            "   - app_unhealthy_combined.fasta\n",
            "   - psen2_healthy_combined.fasta\n",
            "   - psen1_healthy_combined.fasta\n",
            "   - psen1_unhealthy_combined.fasta\n",
            "\n",
            "📊 Available features:\n",
            "   - BioPython: False\n",
            "   - PyTorch: True\n",
            "   - PyTorch Geometric: False\n",
            "   - TensorFlow: True\n",
            "✅ Core data structures and base class defined!\n",
            "✅ Data loading functions defined!\n"
          ]
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Data Loading Functions\n",
        "def load_fasta_file(file_path: Path) -> List[str]:\n",
        "    \"\"\"Load sequences from FASTA file with proper header handling\"\"\"\n",
        "    sequences = []\n",
        "    try:\n",
        "        print(f\"📖 Reading file: {file_path}\")\n",
        "        current_seq = \"\"\n",
        "\n",
        "        with open(file_path, 'r') as f:\n",
        "            for line in f:\n",
        "                line = line.strip()\n",
        "                if line.startswith('>'):\n",
        "                    # Save previous sequence if exists\n",
        "                    if current_seq:\n",
        "                        sequences.append(current_seq.upper())\n",
        "                        current_seq = \"\"\n",
        "                    # Print header info (first few characters)\n",
        "                    print(f\"   Header: {line[:80]}...\")\n",
        "                else:\n",
        "                    # Accumulate sequence data\n",
        "                    current_seq += line\n",
        "\n",
        "            # Don't forget the last sequence\n",
        "            if current_seq:\n",
        "                sequences.append(current_seq.upper())\n",
        "\n",
        "        print(f\"✅ Successfully loaded {len(sequences)} sequences from {file_path.name}\")\n",
        "        if sequences:\n",
        "            print(f\"   Sample lengths: {[len(s) for s in sequences[:3]]}\")\n",
        "            print(f\"   Sample sequence: {sequences[0][:50]}...\")\n",
        "\n",
        "    except Exception as e:\n",
        "        print(f\"❌ Error loading FASTA file {file_path}: {e}\")\n",
        "\n",
        "    return sequences\n",
        "\n",
        "def load_app_genomic_data(dataset_path: str, max_sequences: int = 100) -> Tuple[List[str], List[int]]:\n",
        "    \"\"\"Load APP genomic data (healthy and unhealthy) - preserving full gene lengths\"\"\"\n",
        "    print(\"🧬 Loading APP genomic data...\")\n",
        "\n",
        "    sequences = []\n",
        "    labels = []\n",
        "\n",
        "    # Focus on APP files only\n",
        "    app_files = [\n",
        "        ('APP_healthy_combined.fasta', 0),\n",
        "        ('app_unhealthy_combined.fasta', 1)\n",
        "    ]\n",
        "\n",
        "    print(f\"🔍 Looking for APP files in: {dataset_path}\")\n",
        "\n",
        "    for file_name, label in app_files:\n",
        "        file_path = Path(dataset_path) / file_name\n",
        "        print(f\"   Checking: {file_name} - Exists: {file_path.exists()}\")\n",
        "\n",
        "        if file_path.exists():\n",
        "            file_sequences = load_fasta_file(file_path)\n",
        "            sequences.extend(file_sequences)\n",
        "            labels.extend([label] * len(file_sequences))\n",
        "            print(f\"✅ Loaded {len(file_sequences)} sequences from {file_name}\")\n",
        "        else:\n",
        "            print(f\"❌ File not found: {file_name}\")\n",
        "\n",
        "    print(f\"\\n🔍 Raw APP data loaded: {len(sequences)} sequences\")\n",
        "    if sequences:\n",
        "        print(f\"   Length range: {min(len(s) for s in sequences)} - {max(len(s) for s in sequences)}\")\n",
        "        print(f\"   Sample characters: {set(''.join(sequences[:3])[:100])}\")\n",
        "\n",
        "    # Preserve full gene lengths - only basic quality filtering\n",
        "    processed_sequences = []\n",
        "    processed_labels = []\n",
        "\n",
        "    print(f\"🧬 Processing APP sequences (preserving full gene lengths)...\")\n",
        "\n",
        "    for seq, label in zip(sequences, labels):\n",
        "        # Only basic quality checks - keep original lengths\n",
        "        if len(seq) < 50:  # Only filter extremely short sequences\n",
        "            continue\n",
        "\n",
        "        # Check for valid nucleotides (allow common IUPAC codes)\n",
        "        valid_chars = set('ATGCNRYSWKMBDHV')  # Standard + ambiguous nucleotides\n",
        "        invalid_chars = set(seq) - valid_chars\n",
        "        if invalid_chars:\n",
        "            print(f\"   ⚠️  Sequence contains invalid characters: {invalid_chars}\")\n",
        "            continue\n",
        "\n",
        "        # Allow sequences with reasonable N content for genomic data\n",
        "        n_content = seq.count('N') / len(seq)\n",
        "        if n_content > 0.5:  # Skip if more than 50% N's\n",
        "            print(f\"   ⚠️  Sequence has high N content ({n_content:.1%}), skipping...\")\n",
        "            continue\n",
        "\n",
        "        # Keep the full sequence\n",
        "        processed_sequences.append(seq)\n",
        "        processed_labels.append(label)\n",
        "\n",
        "        if len(processed_sequences) >= max_sequences:\n",
        "            break\n",
        "\n",
        "    print(f\"\\n📊 APP data processing results:\")\n",
        "    print(f\"   Final sequences: {len(processed_sequences)}\")\n",
        "    if processed_sequences:\n",
        "        print(f\"   Healthy sequences (0): {sum(1 for l in processed_labels if l == 0)}\")\n",
        "        print(f\"   Unhealthy sequences (1): {sum(1 for l in processed_labels if l == 1)}\")\n",
        "        print(f\"   Average length: {np.mean([len(s) for s in processed_sequences]):.0f} bp\")\n",
        "        print(f\"   Length range: {min(len(s) for s in processed_sequences)} - {max(len(s) for s in processed_sequences)} bp\")\n",
        "\n",
        "        # Show individual sequence info\n",
        "        print(f\"\\n🔍 Individual sequence details:\")\n",
        "        for i, (seq, label) in enumerate(zip(processed_sequences[:5], processed_labels[:5])):\n",
        "            status = \"Healthy\" if label == 0 else \"Unhealthy\"\n",
        "            print(f\"   Sequence {i+1}: {len(seq):,} bp - {status}\")\n",
        "            print(f\"     Preview: {seq[:50]}...\")\n",
        "            if i < 4 and i+1 < len(processed_sequences):\n",
        "                print()\n",
        "\n",
        "    return processed_sequences, processed_labels\n",
        "\n",
        "def analyze_sequence_composition(sequences: List[str], labels: List[int]):\n",
        "    \"\"\"Analyze nucleotide composition of sequences\"\"\"\n",
        "    print(\"\\n🔬 Analyzing APP sequence composition...\")\n",
        "\n",
        "    healthy_seqs = [sequences[i] for i, l in enumerate(labels) if l == 0]\n",
        "    unhealthy_seqs = [sequences[i] for i, l in enumerate(labels) if l == 1]\n",
        "\n",
        "    def get_composition(seqs):\n",
        "        total_length = sum(len(s) for s in seqs)\n",
        "        if total_length == 0:\n",
        "            return {'A': 0, 'T': 0, 'G': 0, 'C': 0, 'N': 0}\n",
        "\n",
        "        counts = {'A': 0, 'T': 0, 'G': 0, 'C': 0, 'N': 0}\n",
        "        for seq in seqs:\n",
        "            for nuc in seq:\n",
        "                if nuc in counts:\n",
        "                    counts[nuc] += 1\n",
        "\n",
        "        # Convert to percentages\n",
        "        return {nuc: (count/total_length)*100 for nuc, count in counts.items()}\n",
        "\n",
        "    if healthy_seqs:\n",
        "        healthy_comp = get_composition(healthy_seqs)\n",
        "        print(f\"   Healthy APP composition: {healthy_comp}\")\n",
        "\n",
        "    if unhealthy_seqs:\n",
        "        unhealthy_comp = get_composition(unhealthy_seqs)\n",
        "        print(f\"   Unhealthy APP composition: {unhealthy_comp}\")\n",
        "\n",
        "    # Visualize composition\n",
        "    if healthy_seqs and unhealthy_seqs:\n",
        "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n",
        "\n",
        "        # Healthy composition\n",
        "        nucleotides = ['A', 'T', 'G', 'C']\n",
        "        healthy_values = [healthy_comp[nuc] for nuc in nucleotides]\n",
        "        ax1.bar(nucleotides, healthy_values, color=['red', 'blue', 'green', 'orange'], alpha=0.7)\n",
        "        ax1.set_title('Healthy APP Sequences\\nNucleotide Composition')\n",
        "        ax1.set_ylabel('Percentage (%)')\n",
        "        ax1.set_ylim(0, max(max(healthy_values), max([unhealthy_comp[nuc] for nuc in nucleotides])) * 1.1)\n",
        "\n",
        "        # Unhealthy composition\n",
        "        unhealthy_values = [unhealthy_comp[nuc] for nuc in nucleotides]\n",
        "        ax2.bar(nucleotides, unhealthy_values, color=['red', 'blue', 'green', 'orange'], alpha=0.7)\n",
        "        ax2.set_title('Unhealthy APP Sequences\\nNucleotide Composition')\n",
        "        ax2.set_ylabel('Percentage (%)')\n",
        "        ax2.set_ylim(0, max(max(healthy_values), max(unhealthy_values)) * 1.1)\n",
        "\n",
        "        plt.tight_layout()\n",
        "        plt.show()\n",
        "\n",
        "print(\"✅ Data loading functions defined!\")"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/"
        },
        "id": "OLDsyz5qRgD-",
        "outputId": "ffa362de-7c18-4a98-86de-c072158f1f63"
      },
      "execution_count": 2,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "✅ Data loading functions defined!\n"
          ]
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Load APP genomic data\n",
        "print(\"\\n\" + \"=\"*60)\n",
        "print(\"🧬 LOADING APP GENOMIC DATA\")\n",
        "print(\"=\"*60)\n",
        "\n",
        "app_sequences, app_labels = load_app_genomic_data(dataset_path, max_sequences=100)\n",
        "\n",
        "if len(app_sequences) == 0:\n",
        "    print(\"❌ No APP sequences loaded! Please check the dataset path and files.\")\n",
        "else:\n",
        "    print(f\"\\n✅ Successfully loaded {len(app_sequences)} APP gene sequences\")\n",
        "\n",
        "    # Separate sequences by health status for analysis\n",
        "    healthy_sequences = [app_sequences[i] for i, label in enumerate(app_labels) if label == 0]\n",
        "    unhealthy_sequences = [app_sequences[i] for i, label in enumerate(app_labels) if label == 1]\n",
        "\n",
        "    print(f\"\\n📊 APP Gene Sequence Organization:\")\n",
        "    print(f\"   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\")\n",
        "    print(f\"   🟢 HEALTHY APP SEQUENCES: {len(healthy_sequences)}\")\n",
        "    if healthy_sequences:\n",
        "        for i, seq in enumerate(healthy_sequences[:3]):  # Show first 3\n",
        "            print(f\"      Sequence {i+1}: {len(seq):,} bp\")\n",
        "            print(f\"         Preview: {seq[:80]}...\")\n",
        "            print()\n",
        "\n",
        "    print(f\"   🔴 UNHEALTHY APP SEQUENCES: {len(unhealthy_sequences)}\")\n",
        "    if unhealthy_sequences:\n",
        "        for i, seq in enumerate(unhealthy_sequences[:3]):  # Show first 3\n",
        "            print(f\"      Sequence {i+1}: {len(seq):,} bp\")\n",
        "            print(f\"         Preview: {seq[:80]}...\")\n",
        "            print()\n",
        "\n",
        "    # Analyze sequence composition\n",
        "    analyze_sequence_composition(app_sequences, app_labels)\n",
        "\n",
        "    # Display sequence length distribution - preserving original lengths\n",
        "    plt.figure(figsize=(14, 5))\n",
        "\n",
        "    plt.subplot(1, 3, 1)\n",
        "    seq_lengths = [len(s) for s in app_sequences]\n",
        "    plt.hist(seq_lengths, bins=15, alpha=0.7, edgecolor='black', color='skyblue')\n",
        "    plt.title('APP Gene Sequence Lengths\\n(Full Gene Preserved)')\n",
        "    plt.xlabel('Sequence Length (bp)')\n",
        "    plt.ylabel('Count')\n",
        "    plt.ticklabel_format(style='scientific', axis='x', scilimits=(0,0))\n",
        "    plt.grid(True, alpha=0.3)\n",
        "\n",
        "    plt.subplot(1, 3, 2)\n",
        "    if healthy_sequences:\n",
        "        healthy_lengths = [len(s) for s in healthy_sequences]\n",
        "        plt.hist(healthy_lengths, bins=10, alpha=0.7, label='Healthy', color='green', edgecolor='black')\n",
        "    if unhealthy_sequences:\n",
        "        unhealthy_lengths = [len(s) for s in unhealthy_sequences]\n",
        "        plt.hist(unhealthy_lengths, bins=10, alpha=0.7, label='Unhealthy', color='red', edgecolor='black')\n",
        "\n",
        "    plt.title('APP Sequences by Health Status\\n(Full Length Distribution)')\n",
        "    plt.xlabel('Sequence Length (bp)')\n",
        "    plt.ylabel('Count')\n",
        "    plt.ticklabel_format(style='scientific', axis='x', scilimits=(0,0))\n",
        "    plt.legend()\n",
        "    plt.grid(True, alpha=0.3)\n",
        "\n",
        "    # Show sequence length summary\n",
        "    plt.subplot(1, 3, 3)\n",
        "    categories = ['Healthy', 'Unhealthy', 'Overall']\n",
        "    avg_lengths = [\n",
        "        np.mean([len(s) for s in healthy_sequences]) if healthy_sequences else 0,\n",
        "        np.mean([len(s) for s in unhealthy_sequences]) if unhealthy_sequences else 0,\n",
        "        np.mean([len(s) for s in app_sequences])\n",
        "    ]\n",
        "    colors = ['green', 'red', 'blue']\n",
        "\n",
        "    bars = plt.bar(categories, avg_lengths, color=colors, alpha=0.7, edgecolor='black')\n",
        "    plt.title('Average APP Gene Lengths')\n",
        "    plt.ylabel('Average Length (bp)')\n",
        "    plt.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))\n",
        "    plt.grid(True, alpha=0.3, axis='y')\n",
        "\n",
        "    # Add value labels on bars\n",
        "    for bar, length in zip(bars, avg_lengths):\n",
        "        if length > 0:\n",
        "            plt.text(bar.get_x() + bar.get_width()/2., bar.get_height() + max(avg_lengths)*0.01,\n",
        "                    f'{length:,.0f}', ha='center', va='bottom', fontweight='bold')\n",
        "\n",
        "    plt.tight_layout()\n",
        "    plt.show()\n",
        "\n",
        "    # Detailed sequence analysis\n",
        "    print(f\"\\n🔍 Detailed APP Gene Analysis:\")\n",
        "    print(f\"   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\")\n",
        "    print(f\"   📊 SEQUENCE STATISTICS:\")\n",
        "    print(f\"      Total APP sequences: {len(app_sequences)}\")\n",
        "    print(f\"      Healthy sequences: {len(healthy_sequences)}\")\n",
        "    print(f\"      Unhealthy sequences: {len(unhealthy_sequences)}\")\n",
        "    print(f\"      Overall average length: {np.mean([len(s) for s in app_sequences]):,.0f} bp\")\n",
        "    print(f\"      Length range: {min(len(s) for s in app_sequences):,} - {max(len(s) for s in app_sequences):,} bp\")\n",
        "    print(f\"      Total nucleotides: {sum(len(s) for s in app_sequences):,} bp\")\n",
        "\n",
        "    if healthy_sequences:\n",
        "        print(f\"      Healthy avg length: {np.mean([len(s) for s in healthy_sequences]):,.0f} bp\")\n",
        "    if unhealthy_sequences:\n",
        "        print(f\"      Unhealthy avg length: {np.mean([len(s) for s in unhealthy_sequences]):,.0f} bp\")\n",
        "\n",
        "# Configuration for motif discovery\n",
        "print(\"\\n\" + \"=\"*60)\n",
        "print(\"⚙️  CONFIGURING MOTIF DISCOVERY PARAMETERS\")\n",
        "print(\"=\"*60)\n",
        "\n",
        "# Motif discovery configuration\n",
        "motif_length = 8  # Common length for regulatory motifs\n",
        "config = OptimizationConfig(\n",
        "    population_size=30,  # Smaller for faster execution\n",
        "    max_generations=50,   # Reduced for demonstration\n",
        "    mutation_rate=0.1,\n",
        "    crossover_rate=0.8,\n",
        "    elitism_size=3,\n",
        "    convergence_threshold=1e-4,\n",
        "    max_stagnation=15,\n",
        "    inertia_weight=0.9,\n",
        "    cognitive_coef=2.0,\n",
        "    social_coef=2.0,\n",
        "    gibbs_iterations=30\n",
        ")\n",
        "\n",
        "print(f\"🎯 Motif Discovery Configuration:\")\n",
        "print(f\"   Target: APP gene motifs\")\n",
        "print(f\"   Motif length: {motif_length} bp\")\n",
        "print(f\"   Population size: {config.population_size}\")\n",
        "print(f\"   Max generations: {config.max_generations}\")\n",
        "print(f\"   Mutation rate: {config.mutation_rate}\")\n",
        "print(f\"   Convergence threshold: {config.convergence_threshold}\")\n",
        "\n",
        "# Use all sequences for analysis (preserving full gene lengths)\n",
        "analysis_sequences = app_sequences\n",
        "analysis_labels = app_labels\n",
        "\n",
        "print(f\"\\n📊 Analysis Dataset:\")\n",
        "print(f\"   Using all {len(analysis_sequences)} APP gene sequences\")\n",
        "print(f\"   Healthy genes: {sum(1 for l in analysis_labels if l == 0)}\")\n",
        "print(f\"   Unhealthy genes: {sum(1 for l in analysis_labels if l == 1)}\")\n",
        "print(f\"   Note: Full gene lengths preserved for comprehensive motif discovery\")\n"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/",
          "height": 1000
        },
        "id": "OVFrRYg8XlTq",
        "outputId": "1cac4d40-6c4f-4586-b240-baf09434e385"
      },
      "execution_count": 3,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "\n",
            "============================================================\n",
            "🧬 LOADING APP GENOMIC DATA\n",
            "============================================================\n",
            "🧬 Loading APP genomic data...\n",
            "🔍 Looking for APP files in: /content/drive/MyDrive/Dataset_from reference genome\n",
            "   Checking: APP_healthy_combined.fasta - Exists: True\n",
            "📖 Reading file: /content/drive/MyDrive/Dataset_from reference genome/APP_healthy_combined.fasta\n",
            "   Header: >NC_000021.9:25880550-26171128_1 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000021.9:25880550-26171128_2 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000021.9:25880550-26171128_3 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000021.9:25880550-26171128_1 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000021.9:25880550-26171128_5 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000021.9:25880550-26171128_6 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000021.9:25880550-26171128_7 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "✅ Successfully loaded 7 sequences from APP_healthy_combined.fasta\n",
            "   Sample lengths: [290578, 290576, 290576]\n",
            "   Sample sequence: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAA...\n",
            "✅ Loaded 7 sequences from APP_healthy_combined.fasta\n",
            "   Checking: app_unhealthy_combined.fasta - Exists: True\n",
            "📖 Reading file: /content/drive/MyDrive/Dataset_from reference genome/app_unhealthy_combined.fasta\n",
            "   Header: >NC_000014.9:25880550-26171128_1 Homo sapiens chromosome 14, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000014.9:25880550-26171128_2 Homo sapiens chromosome 14, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000014.9:25880550-26171128_3 Homo sapiens chromosome 14, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000014.9:25880550-26171128_4 Homo sapiens chromosome 14, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000021.9:25880550-26171128_5 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "   Header: >NC_000021.9:25880550-26171128_6 Homo sapiens chromosome 21, GRCh38.p14 Primary ...\n",
            "✅ Successfully loaded 6 sequences from app_unhealthy_combined.fasta\n",
            "   Sample lengths: [290576, 290576, 290576]\n",
            "   Sample sequence: GGTGGAGCACCATCTTTACTTCACATGCACTGTGCATTCCTCTTGTGCCT...\n",
            "✅ Loaded 6 sequences from app_unhealthy_combined.fasta\n",
            "\n",
            "🔍 Raw APP data loaded: 13 sequences\n",
            "   Length range: 290576 - 290582\n",
            "   Sample characters: {'G', 'A', 'T', 'C'}\n",
            "🧬 Processing APP sequences (preserving full gene lengths)...\n",
            "\n",
            "📊 APP data processing results:\n",
            "   Final sequences: 13\n",
            "   Healthy sequences (0): 7\n",
            "   Unhealthy sequences (1): 6\n",
            "   Average length: 290578 bp\n",
            "   Length range: 290576 - 290582 bp\n",
            "\n",
            "🔍 Individual sequence details:\n",
            "   Sequence 1: 290,578 bp - Healthy\n",
            "     Preview: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAA...\n",
            "\n",
            "   Sequence 2: 290,576 bp - Healthy\n",
            "     Preview: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAA...\n",
            "\n",
            "   Sequence 3: 290,576 bp - Healthy\n",
            "     Preview: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAA...\n",
            "\n",
            "   Sequence 4: 290,580 bp - Healthy\n",
            "     Preview: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAA...\n",
            "\n",
            "   Sequence 5: 290,582 bp - Healthy\n",
            "     Preview: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAA...\n",
            "\n",
            "✅ Successfully loaded 13 APP gene sequences\n",
            "\n",
            "📊 APP Gene Sequence Organization:\n",
            "   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n",
            "   🟢 HEALTHY APP SEQUENCES: 7\n",
            "      Sequence 1: 290,578 bp\n",
            "         Preview: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAATTGAAGACACATCTTAAAAGAAGGGTTTGT...\n",
            "\n",
            "      Sequence 2: 290,576 bp\n",
            "         Preview: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAATTGAAGACACATCTTAAAAGAAGGGTTTGT...\n",
            "\n",
            "      Sequence 3: 290,576 bp\n",
            "         Preview: TGCTCCTCCAAGAATGTATTTATTTACATGAAAACACCATTTTATACAAATTGAAGACACATCTTAAAAGAAGGGTTTGT...\n",
            "\n",
            "   🔴 UNHEALTHY APP SEQUENCES: 6\n",
            "      Sequence 1: 290,576 bp\n",
            "         Preview: GGTGGAGCACCATCTTTACTTCACATGCACTGTGCATTCCTCTTGTGCCTTCTTATAGGCCCCCTAAATATTAATATTTG...\n",
            "\n",
            "      Sequence 2: 290,576 bp\n",
            "         Preview: GGTGGAGCACCATCTTTACTTCACATGCACTGTGCATTCCTCTTGTGCCTTCTTATAGGCCCCCTAAATATTAATATTTG...\n",
            "\n",
            "      Sequence 3: 290,576 bp\n",
            "         Preview: GGTGGAGCACCATCTTTACTTCACATGCACTGTGCATTCCTCTTGTGCCTTCTTATAGGCCCCCTAAATATTAATATTTG...\n",
            "\n",
            "\n",
            "🔬 Analyzing APP sequence composition...\n",
            "   Healthy APP composition: {'A': 31.998574273002138, 'T': 28.26975738059536, 'G': 19.500602246749096, 'C': 20.2310660996534, 'N': 0.0}\n",
            "   Unhealthy APP composition: {'A': 32.34126733759075, 'T': 29.840731049530074, 'G': 18.738707838294523, 'C': 19.07929377458465, 'N': 0.0}\n"
          ]
        },
        {
          "output_type": "display_data",
          "data": {
            "text/plain": [
              "<Figure size 1200x500 with 2 Axes>"
            ],
            "image/png": "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\n"
          },
          "metadata": {}
        },
        {
          "output_type": "display_data",
          "data": {
            "text/plain": [
              "<Figure size 1400x500 with 3 Axes>"
            ],
            "image/png": "iVBORw0KGgoAAAANSUhEUgAABW4AAAHpCAYAAAASzqVtAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAA09BJREFUeJzs3XdcU1f/B/BPWAl7GUQBEQVFERdaB6I4cZe6N2hdLa7HalvbWsVqqbWuakXt0NbCo3W3tg7cdbXuOlrrALeCKBBQEMj5/eEveQxhJKwE+byfF79fc+6553zPzTEn9+TecyVCCAEiIiIiIiIiIiIiMhomhg6AiIiIiIiIiIiIiDRx4paIiIiIiIiIiIjIyHDiloiIiIiIiIiIiMjIcOKWiIiIiIiIiIiIyMhw4paIiIiIiIiIiIjIyHDiloiIiIiIiIiIiMjIcOKWiIiIiIiIiIiIyMhw4paIiIiIiIiIiIjIyHDiloiIiIiIiIiIiMjIcOKWiKgchYeHw8bGxtBhEBFREYKDg9GgQQNDh1HmEhISIJFIsHbtWp3zfvHFF2UfGBERERFx4pY0rVixAhKJBC1atCgwj0QiUf+ZmJigevXq6NKlCw4ePKiRr2bNmhp5XVxcEBQUhK1bt+ocz19//YWRI0fCy8sLMpkMNjY2aNy4Md59913cuHGjuM0sNxcuXEC/fv3g6ekJmUwGNzc3dO7cGcuWLTN0aBXawYMHIZFIsGnTJkOHkq+nT59i9uzZWv8miKhiMKax8MiRI+jWrRvc3Nwgk8lQo0YN9OrVC7GxsSVpIhkRiUSCCRMm5Ltt7dq1kEgkOHXqVLnG9Ntvv2H27NllUnZCQgJGjhyJ2rVrQyaTwdXVFW3btsWsWbM08q1YsUKnyeSC3Lt3D7Nnz8a5c+dKFjARERGRAXHiljTExMSgZs2a+PPPP3Ht2rUC83Xu3Bnr1q3D999/j/Hjx+Ovv/5Chw4dsHPnTo18jRs3xrp167Bu3TpMmzYN9+7dQ58+fbBy5coiY/n666/RtGlT7Ny5E3369MGyZcuwYMECBAYG4ocffoCvry9yc3NL3OaycuzYMTRr1gznz5/HmDFjsHz5cowePRomJiZYunSpocOjMvT06VNERkZy4paogjKWsXDjxo1o27YtHj58iMmTJ2PZsmUYNmwYnjx5gq+//rpU2kqUn99++w2RkZGlXu61a9fQpEkT7N69G4MHD8by5csREREBZ2dnzJ8/XyNvaUzcRkZGcuKWiIiIKjQzQwdAxiM+Ph7Hjh3Dli1bMG7cOMTExGhd/aBSp04dDBs2TP36jTfeQMOGDbFkyRJ069ZNne7m5qaRb8SIEfD29sbixYsxfvz4AmM5duwY3nrrLQQGBmLHjh2wtbXV2L5w4ULMmzevuE0tF/PmzYO9vT1OnjwJBwcHjW2JiYmGCYqIiAplTGPh7NmzUb9+fZw4cQIWFhYa2ziOUEW0ePFipKen49y5c/D09NTYxj5NREREpI1X3JJaTEwMHB0d0aNHD/Tr1w8xMTE67+vv748qVaogPj6+0Hyurq6oV69ekfkiIyMhkUgQExOjNWkLADKZDJ988glMTU010v/44w907doV9vb2sLKyQrt27XD06FGNPLNnz4ZEIsG1a9cQHh4OBwcH2NvbY+TIkXj69KlWXT/++CMCAgJgaWkJJycnDBo0CLdv3y40fgC4fv06/Pz8tCZtAcDFxaXY9axevRq1a9eGpaUlXnvtNfz+++8IDg5GcHCwOo/q1sqEhASNfVVLDOS9ErSsjttrr70GKysrODo6om3bttizZ49Gnp07dyIoKAjW1tawtbVFjx49cOnSJa2yiislJQVTpkyBh4cHpFIpvL29MX/+fCiVSnWel9frUx1bqVSK5s2b4+TJk1plbty4EfXr14dMJkODBg2wdetWhIeHo2bNmury5HI5gP/1Y4lEonXL6d27dxEaGgobGxvI5XJMmzZN6wry9evXIyAgALa2trCzs4O/vz+v1iYqY8Y0Fl6/fh3NmzfXmrQFtMcRpVKJJUuWwM/PDzKZDFWrVsW4cePw5MkTjXxCCMydOxfu7u6wsrJC+/btcenSJdSsWRPh4eHqfKrP/LwKGl90+TxXrfGty+efUqnE0qVL4e/vD5lMBrlcjq5du2otGaDL2Hn16lX07dsXrq6ukMlkcHd3x6BBg5CamqrVvvycPn0arVu3hqWlJby8vDSulE5PT4e1tTUmT56std+dO3dgamqKqKgonerRxz///IN+/frByckJMpkMzZo1w88//6yR5/Hjx5g2bRr8/f1hY2MDOzs7dOvWDefPny+07PDwcHz11VcANJcEyUuXMTOv69evw93dXWvSFtDs0zVr1sSlS5dw6NAhdf2q7zm6tOvgwYNo3rw5AGDkyJHqMlRX8Obt7yp5v08BwLJly+Dn56f+PtOsWTMuVUJERETlhhO3pBYTE4M+ffrAwsICgwcPxtWrV3X6Eg4AT548wZMnT+Ds7FxovuzsbNy+fbvQfE+fPsX+/fsRHBwMd3d3nePfv38/2rZti7S0NMyaNQuffvopUlJS0KFDB/z5559a+QcMGACFQoGoqCgMGDAAa9eu1botcN68eRgxYgR8fHywaNEiTJkyBfv27UPbtm2RkpJSaDyenp44ffo0Ll68WGTsutbz7bffYty4cXB1dcXnn3+OwMBA9O7dW6eJ5IKUxXGLjIzE8OHDYW5ujjlz5iAyMhIeHh7Yv3+/Os+6devQo0cP2NjYYP78+Zg5cyYuX76MNm3aaE0IFMfTp0/Rrl07/PjjjxgxYgS+/PJLBAYGYsaMGZg6dapW/tjYWCxYsADjxo3D3LlzkZCQgD59+iA7O1ud59dff8XAgQNhbm6OqKgo9OnTB2+++SZOnz6tziOXyxEdHQ3gxdV3qtuj+/Tpo86Tm5uLkJAQODs744svvkC7du2wcOFCrF69Wp0nLi4OgwcPhqOjI+bPn4/PPvsMwcHBWhPqRFS6jGUsBF6MI/v27cOdO3eKrHvcuHGYPn06AgMDsXTpUowcORIxMTEICQnR+Bz7+OOPMXPmTDRq1AgLFixArVq10KVLF2RkZOjUxvzo83muy+cfALz55pvqH97mz5+P999/HzKZDCdOnFDn0WXsfP78OUJCQnDixAlMnDgRX331FcaOHYsbN24UOY4DL97T7t27IyAgAJ9//jnc3d3x1ltv4bvvvgMA2NjY4I033sCGDRu0Jp//+9//QgiBoUOHFllPZmYmHj16pPWXnp6ulffSpUto2bIl/v77b7z//vtYuHAhrK2tERoaqrF28o0bN7Bt2zb07NkTixYtwvTp03HhwgW0a9cO9+7dKzCWcePGoXPnzgCgHsPWrVunkUeXMTM/np6euH37tsb3gfwsWbIE7u7u8PX1Vdf/4Ycf6tyuevXqYc6cOQCAsWPHqsto27ZtofXm9fXXX2PSpEmoX78+lixZgsjISDRu3Bh//PGHXuUQERERFZsgEkKcOnVKABBxcXFCCCGUSqVwd3cXkydP1soLQLz55psiKSlJJCYmij/++EN07NhRABALFy5U5/P09BRdunQRSUlJIikpSZw/f14MGjRIABATJ04sMJbz588LAGLKlCla25KTk9XlJSUliaysLHW8Pj4+IiQkRCiVSnX+p0+fCi8vL9G5c2d12qxZswQAMWrUKI2y33jjDeHs7Kx+nZCQIExNTcW8efM08l24cEGYmZlppee1Z88eYWpqKkxNTUWrVq3Eu+++K3bv3i2eP3+ukU/Xep4/fy5cXFxE48aN1e0WQojVq1cLAKJdu3bqtDVr1ggAIj4+XqPMAwcOCADiwIEDZXbcrl69KkxMTMQbb7whcnNzNfKq6lAoFMLBwUGMGTNGY/uDBw+Evb29VnpeqnZs3LixwDyffPKJsLa2Fv/++69G+vvvvy9MTU3FrVu3hBBCxMfHCwDC2dlZPH78WJ1v+/btAoD45Zdf1Gn+/v7C3d1dKBQKddrBgwcFAOHp6alOS0pKEgDErFmztOIKCwsTAMScOXM00ps0aSICAgLUrydPnizs7OxETk5OoceCiEqPMY2FQgjx7bffCgDCwsJCtG/fXsycOVP8/vvvWp+tv//+uwAgYmJiNNJ37dqlkZ6YmCgsLCxEjx49ND7zP/jgAwFAhIWFqdNUn/l55R1f9Pk81/Xzb//+/QKAmDRpklb9qrh1HTvPnj1b5HhRkHbt2mm9n1lZWaJx48bCxcVFPZ7v3r1bABA7d+7U2L9hw4YaY3NBABT5d/LkSXX+jh07Cn9/f5GZmalOUyqVonXr1sLHx0edlpmZqdVX4uPjhVQq1XgPVOPgmjVr1GkRERH5vv/6jJn5uXjxorC0tBQAROPGjcXkyZPFtm3bREZGhlZePz+/fI+fru06efKkVrtUPD09Nfq7Srt27TTqfP3114Wfn1+hbSIiIiIqS7zilgC8uMKoatWqaN++PYAXt8YNHDgQ69evz/cBYN9++y3kcjlcXFzQokULHD16FFOnTsWUKVM08u3ZswdyuRxyuRyNGjXCxo0bMXz4cK0HULwsLS0NwIurWPKqVauWujy5XK6+LfDcuXO4evUqhgwZguTkZPWVKhkZGejYsSMOHz6scXs8AK11BYOCgpCcnKyuf8uWLVAqlRgwYIDG1S+urq7w8fHBgQMHCj2mnTt3xvHjx9G7d2+cP38en3/+OUJCQuDm5qZxO6Ou9Zw6dQqJiYkYP368xm2z4eHhsLe3LzSWgpTFcdu2bRuUSiU+/vhjmJhofsSobrWMi4tDSkoKBg8erNFmU1NTtGjRoshjq4uNGzciKCgIjo6OGnV06tQJubm5OHz4sEb+gQMHwtHRUaNdwIsre4AXDzm5cOECRowYodE327VrB39/f73jy+84quoCAAcHB2RkZCAuLk7vsomoeIxpLASAUaNGYdeuXQgODsaRI0fwySefICgoCD4+Pjh27Jg638aNG2Fvb4/OnTtrfN4FBATAxsZG/Zm6d+9ePH/+HBMnTtS49T1vvPoozud5UZ9/mzdvhkQiyXdtYVXcuo6dqvFx9+7d+S7rUxQzMzOMGzdO/drCwgLjxo1DYmKi+m6LTp06oXr16hrLaly8eBF//fWXxtrGhXn99dcRFxen9Td9+nSNfI8fP8b+/fvVd7+o2p2cnIyQkBBcvXoVd+/eBQBIpVL1OJybm4vk5GTY2Nigbt26OHPmjN7H4mVFjZkF8fPzw7lz5zBs2DAkJCRg6dKlCA0NRdWqVXV+4F5ZtisvBwcH3LlzR+er7omIiIhKGx9ORsjNzcX69evRvn17jfX2WrRogYULF2Lfvn3o0qWLxj6vv/46JkyYAIlEAltbW/j5+cHa2lqr7BYtWmDu3LmQSCSwsrJCvXr18l3z9WWqNW3zuz1w+/btyM7Oxvnz5zFt2jR1+tWrVwEAYWFhBZabmpqqcZJRo0YNje2qbU+ePIGdnR2uXr0KIQR8fHzyLc/c3LzQdgBA8+bNsWXLFjx//hznz5/H1q1bsXjxYvTr1w/nzp1D/fr1da7n5s2bAKCVz9zcHLVq1SoylvyUxXG7fv06TExMUL9+/SLr7dChQ77b7ezsdGtAIa5evYq//vpLvd5sXnkfglJYu4D/HX9vb2+tsry9vfU6WVSt15i3vpfXonz77bfx008/oVu3bnBzc0OXLl0wYMAAdO3aVed6iEh3xjYWqoSEhCAkJARPnz7F6dOnsWHDBqxcuRI9e/bEP//8AxcXF1y9ehWpqan5rp8O/O/zrqBxRC6Xa3zO60Pfz3NdPv+uX7+O6tWrw8nJqdB6dRk7vby8MHXqVCxatAgxMTEICgpC7969MWzYMJ1+9KxevbrWe1qnTh0AL9Y0b9myJUxMTDB06FBER0fj6dOnsLKyQkxMDGQyGfr3719kHQDg7u6OTp06aaXnXSbj2rVrEEJg5syZmDlzZr5lJSYmws3NTb1O8IoVKxAfH6/x40NRy3QUpagxszB16tTBunXrkJubi8uXL2PHjh34/PPPMXbsWHh5eeV7HF5Wlu3K67333sPevXvx2muvwdvbG126dMGQIUMQGBhYqvUQERERFYQTt4T9+/fj/v37WL9+PdavX6+1PSYmRutktaATjLyqVKmiU76XeXt7w8zMLN+1Ydu1awfgxRUwL1NdFbpgwQI0btw433LzXsGb98FmKkIIdZkSiQQ7d+7MN29+VwQXxMLCAs2bN0fz5s1Rp04djBw5Ehs3bsSsWbNKtR6V/B4iAiDfh78ApXvcdKGqd926dXB1ddXanvf9LQ6lUonOnTvj3XffzXe76sRbpTTapauC6nqZi4sLzp07h927d2Pnzp3YuXMn1qxZgxEjRuD7778v9ZiIKjtjGwvzsrKyQlBQEIKCglClShVERkZi586dCAsLg1KphIuLS4EPUivoB6zC6DuO6Pp5rsvnny70GTsXLlyI8PBwbN++HXv27MGkSZMQFRWFEydO6LWWfmFGjBiBBQsWYNu2bRg8eDBiY2PRs2fPYt8RUxDV8Z42bRpCQkLyzaP6gfHTTz/FzJkzMWrUKHzyySdwcnKCiYkJpkyZonU3jb5KY8w0NTWFv78//P390apVK7Rv3x4xMTFF/lspjXYV1r9fblu9evVw5coV7NixA7t27cLmzZuxYsUKfPzxx1rr+xMRERGVBU7cEmJiYuDi4qJ+gvDLtmzZgq1bt2LlypWwtLQsl3isra0RHByMQ4cO4e7du3Bzcytyn9q1awN4cWVPSU+OXy5TCAEvLy+tSb6SaNasGQDg/v37etWjegLz1atXNa5sys7ORnx8PBo1aqROU135kvfBK6qrrVTK6rgplUpcvny5wMlgVb0uLi6lVm9+daSnp5da+arjf+3aNa1tedMKOiHUl4WFBXr16oVevXpBqVTi7bffxqpVqzBz5sx8r/wlouIztrGwMPmNI3v37kVgYGCh8b08jrx8p0ZSUpLWlZIvjyMvXx1c0DhSmp/ntWvXxu7du/H48eMCr7rVd4xWTRB+9NFHOHbsGAIDA7Fy5UrMnTu30P3u3buHjIwMjatu//33XwBAzZo11WkNGjRAkyZNEBMTA3d3d9y6dQvLli3TobX6Ub1v5ubmRR7vTZs2oX379vj222810lNSUlClSpVC9y2tcUxXeft0YTHo2q7C2uDo6Jjvw+lu3rypdReTtbU1Bg4ciIEDB+L58+fo06cP5s2bhxkzZkAmkxXZNiIiIqKS4Bq3ldyzZ8+wZcsW9OzZE/369dP6mzBhAhQKhcaarOXh448/Rm5uLoYNG5bvkgl5r+gICAhA7dq18cUXX+SbPykpSe8Y+vTpA1NTU0RGRmrVJ4RAcnJyofsfOHAg3ytPfvvtNwBA3bp19aqnWbNmkMvlWLlyJZ4/f67Os3btWq2TD9WJ9MvruObm5mo9tbssjltoaChMTEwwZ84crStfVO0LCQmBnZ0dPv3003yfQF2cevMaMGAAjh8/jt27d2ttS0lJQU5Ojl7lVa9eHQ0aNMAPP/ygcawOHTqECxcuaOS1srJS11NcefuXiYkJGjZsCADIysoqdrlEpM1Yx8J9+/blm553HBkwYAByc3PxySefaOXNyclRfxZ16tQJ5ubmWLZsmcZ4s2TJEq398htHMjIytK74L4vP8759+0IIke8Vjaq4dR0709LStD7v/f39YWJiotNnaU5ODlatWqV+/fz5c6xatQpyuRwBAQEaeYcPH449e/ZgyZIlcHZ2Rrdu3XRrsB5cXFwQHByMVatWaUxyqrx8vE1NTbWOzcaNG9Vr4BZGNVFdknEsP7///nu+/SRvn1bFkF/9urarsDbUrl0bJ06c0Pg+tWPHDty+fVsjX96x2MLCAvXr14cQIt92EBEREZU2XnFbyf38889QKBTo3bt3vttbtmwJuVyOmJgYDBw4sNziCgoKwvLlyzFx4kT4+Phg6NCh8PX1xfPnz/Hvv/8iJiYGFhYW6tsyTUxM8M0336Bbt27w8/PDyJEj4ebmhrt37+LAgQOws7PDL7/8olcMtWvXxty5czFjxgwkJCQgNDQUtra2iI+Px9atWzF27FiNdXbzmjhxIp4+fYo33nhDHfuxY8ewYcMG1KxZEyNHjtSrHnNzc8ydOxfjxo1Dhw4dMHDgQMTHx2PNmjVaV4f4+fmhZcuWmDFjhvqKpfXr12udvJbFcfP29saHH36ofohOnz59IJVKcfLkSVSvXh1RUVGws7NDdHQ0hg8fjqZNm2LQoEGQy+W4desWfv31VwQGBmL58uVF1rV582b8888/WulhYWGYPn06fv75Z/Ts2RPh4eEICAhARkYGLly4gE2bNiEhIaHIK47y+vTTT/H6668jMDAQI0eOxJMnT7B8+XI0aNBAYzLX0tIS9evXx4YNG1CnTh04OTmhQYMGaNCggc51jR49Go8fP0aHDh3g7u6OmzdvYtmyZWjcuDHq1aunV9xEVDhjHQtff/11eHl5oVevXqhduzYyMjKwd+9e/PLLL2jevDl69eoF4MUyQuPGjUNUVBTOnTuHLl26wNzcHFevXsXGjRuxdOlS9OvXD3K5HNOmTUNUVBR69uyJ7t274+zZs9i5c6fW52GXLl1Qo0YNvPnmm5g+fTpMTU3x3XffqT+rVUrr8/xl7du3x/Dhw/Hll1/i6tWr6Nq1K5RKJX7//Xe0b98eEyZM0Hns3L9/PyZMmID+/fujTp06yMnJwbp162Bqaoq+ffsWGUv16tUxf/58JCQkoE6dOtiwYQPOnTuH1atXa611P2TIELz77rvYunUr3nrrLZ3Wwi+Or776Cm3atIG/vz/GjBmDWrVq4eHDhzh+/Dju3LmD8+fPAwB69uyJOXPmYOTIkWjdujUuXLiAmJgYndbFV01KT5o0CSEhITA1NcWgQYNKHPv8+fNx+vRp9OnTR/1j5JkzZ/DDDz/AyclJ40F5AQEBiI6Oxty5c+Ht7Q0XFxd06NBB53bVrl0bDg4OWLlyJWxtbWFtbY0WLVrAy8sLo0ePxqZNm9C1a1cMGDAA169fx48//qj+wUKlS5cucHV1RWBgIKpWrYq///4by5cvR48ePdTPZCAiIiIqU4IqtV69egmZTCYyMjIKzBMeHi7Mzc3Fo0ePhBBCABARERFFlu3p6Sl69OhRovjOnj0rRowYIWrUqCEsLCyEtbW1aNiwoXjnnXfEtWvX8s3fp08f4ezsLKRSqfD09BQDBgwQ+/btU+eZNWuWACCSkpI09l2zZo0AIOLj4zXSN2/eLNq0aSOsra2FtbW18PX1FREREeLKlSuFxr5z504xatQo4evrK2xsbISFhYXw9vYWEydOFA8fPtTKr2s9K1asEF5eXkIqlYpmzZqJw4cPi3bt2ol27dpp5Lt+/bro1KmTkEqlomrVquKDDz4QcXFxAoA4cOBAmR+37777TjRp0kRIpVLh6Ogo2rVrJ+Li4jTyHDhwQISEhAh7e3shk8lE7dq1RXh4uDh16lShx/bAgQMCQIF/v//+uxBCCIVCIWbMmCG8vb2FhYWFqFKlimjdurX44osvxPPnz4UQQsTHxwsAYsGCBVr1ABCzZs3SSFu/fr3w9fUVUqlUNGjQQPz888+ib9++wtfXVyPfsWPHREBAgLCwsNAoJywsTFhbW2vVpTq+Kps2bRJdunQRLi4uwsLCQtSoUUOMGzdO3L9/v9BjQ0T6M9ax8L///a8YNGiQqF27trC0tBQymUzUr19ffPjhhyItLU0r/+rVq0VAQICwtLQUtra2wt/fX7z77rvi3r176jy5ubkiMjJSVKtWTVhaWorg4GBx8eJF4enpKcLCwjTKO336tGjRooX6M2jRokUFfubr8nmu6+efEELk5OSIBQsWCF9fX2FhYSHkcrno1q2bOH36tEa+osbOGzduiFGjRonatWsLmUwmnJycRPv27cXevXuLPP7t2rUTfn5+4tSpU6JVq1ZCJpMJT09PsXz58gL36d69uwAgjh07VmT5KoX1JdXxPnnypEb69evXxYgRI4Srq6swNzcXbm5uomfPnmLTpk3qPJmZmeKdd95Rv9eBgYHi+PHjWt8ZVOPgmjVr1Gk5OTli4sSJQi6XC4lEon5/9B0z8zp69KiIiIgQDRo0EPb29sLc3FzUqFFDhIeHi+vXr2vkffDggejRo4ewtbUVANQx69ouIYTYvn27qF+/vjAzM9Nq48KFC4Wbm5uQSqUiMDBQnDp1SquMVatWibZt26q/H9WuXVtMnz5dpKamFtpOIiIiotIiEaIMnrxDROUqODgYAHDw4EGDxlFZNW7cGHK5HHFxcYYOhYioWGrWrIng4GCsXbvW0KFUaG+88QYuXLiQ73roRERERET64hq3REQ6ys7O1lpu4uDBgzh//rx68pyIiCqn+/fv49dff8Xw4cMNHQoRERERvSK4xi0RkY7u3r2LTp06YdiwYahevTr++ecfrFy5Eq6urhg/fryhwyMiIgOIj4/H0aNH8c0338Dc3Bzjxo0zdEhERERE9IrgxC0RkY4cHR0REBCAb775BklJSbC2tkaPHj3w2WefwdnZ2dDhERGRARw6dAgjR45EjRo18P3336sfnEpEREREVFJc45aIiIiIiIiIiIjIyHCNWyIiIiIiIiIiIiIjw4lbIiIiIiIiIiIiIiPDiVsyCp9//jl8fX2hVCrLpPyaNWsiPDxc/frgwYOQSCQ4ePBgmdRHr4bg4GAEBwerX1++fBlmZma4ePGi4YIiolcax8PSFxwcjAYNGpRrnRKJBLNnzy7zevJ7/8qzvQkJCZBIJFi7dm2Z1ZGdnQ0PDw+sWLGizOogIiJ6lai+H2zatMnQoVAp4MQtGVxaWhrmz5+P9957DyYm/+uSEokk3z9DPPQjPj4eEyZMQJ06dWBlZQUrKyvUr18fERER+Ouvv8o9Hn2oPrRVf+bm5qhVqxZGjBiBGzduGDq8CqV+/fro0aMHPv74Y0OHQkSvIGMeD9euXQuJRIJTp06VW536uHfvHmbPno1z586Vetk1a9ZUH3MTExM4ODjA398fY8eOxR9//FFq9cTGxmLJkiWlVl5pMmRs5ubmmDp1KubNm4fMzEyDxEBExmHFihWQSCRo0aKFoUMxWrm5uahevTokEgl27tyZb57Zs2drfJ9Qndt+9NFHSEtLU+dTjf2qP5lMhjp16mDChAl4+PChTvFkZWVh2bJlaNOmDRwdHWFhYYHq1aujd+/e+O9//4vc3NxSaXdZqVmzJnr27GnoMApkzN8dqPSYGToAou+++w45OTkYPHiw1rbOnTtjxIgRGmmWlpblFRoAYMeOHRg4cCDMzMwwdOhQNGrUCCYmJvjnn3+wZcsWREdHIz4+Hp6enuUal74mTZqE5s2bIzs7G2fOnMHq1avx66+/4sKFC6hevbqhw6swxo8fj+7du+P69euoXbu2ocMholeIsY+HxuzevXuIjIxEzZo10bhx41Ivv3HjxnjnnXcAAAqFAn///Tc2btyIr7/+Gv/5z3+waNEijfzPnj2DmZl+X7NjY2Nx8eJFTJkyRed92rZti2fPnsHCwkKvuvRVUGyenp549uwZzM3Ny7T+kSNH4v3330dsbCxGjRpVpnURkfGKiYlBzZo18eeff+LatWvw9vY2dEhGZ//+/bh//z5q1qyJmJgYdOvWrcC80dHRsLGxQXp6Ovbs2YN58+Zh//79OHr0KCQSiTrfnDlz4OXlhczMTBw5cgTR0dH47bffcPHiRVhZWRVYflJSErp164bTp08jJCQEH330EZycnPDgwQPs3bsXQ4YMwbVr1zBz5sxSPQaVSXG+O1DFw4lbMrg1a9agd+/ekMlkWtvq1KmDYcOGGSCqF65fv45BgwbB09MT+/btQ7Vq1TS2z58/HytWrNC4MspYBQUFoV+/fgBenADVqVMHkyZNwvfff48ZM2bku09GRgasra3LM8xCPX36tNAvB+WhU6dOcHR0xPfff485c+YYNBYierUY83hY2bm5uWkd//nz52PIkCFYvHgxfHx88NZbb6m35fcelqbMzExYWFjAxMSkzOsqjOoKrLLm4OCALl26YO3atZy4Jaqk4uPjcezYMWzZsgXjxo1DTEwMZs2aVa4xKJVKPH/+3KCfu0X58ccf0bRpU4SFheGDDz4o9HyuX79+qFKlCoAXF6f07dsXW7ZswYkTJ9CqVSt1vm7duqFZs2YAgNGjR8PZ2RmLFi3C9u3b8/2xWWX48OE4e/YsNm/ejD59+mhsmzFjBk6dOoUrV66UtMlErzzjn22iV1p8fDz++usvdOrUSe99w8PDUbNmTa101a0fpeHzzz9HRkYG1qxZozVpCwBmZmaYNGkSPDw8NNL/+ecf9OvXD05OTpDJZGjWrBl+/vlnjTyqW0+OHj2KqVOnQi6Xw9raGm+88QaSkpK06tq5cyeCgoJgbW0NW1tb9OjRA5cuXSp22zp06ADgxXsA/O+4Xb58GUOGDIGjoyPatGmjzv/jjz8iICAAlpaWcHJywqBBg3D79m2NMq9evYq+ffvC1dUVMpkM7u7uGDRoEFJTUzXy6VKWao2+06dPo23btrCyssIHH3yAnj17olatWvm2qVWrVuovFfrUBQCrV69G7dq1YWlpiddeew2///57vnWYm5sjODgY27dvz3c7EVFxGPt4qKu7d+9i1KhRqFq1KqRSKfz8/PDdd99p5FEt4fPTTz9h3rx5cHd3h0wmQ8eOHXHt2jWtMr/66ivUqlVL4/P55TXIDx48iObNmwN48cOk6pbOvOuuXr58Ge3bt4eVlRXc3Nzw+eefl6itlpaWWLduHZycnDBv3jwIIdTb8q5xq1AoMGXKFNSsWRNSqRQuLi7o3Lkzzpw5A+DFmPfrr7/i5s2b6vhV76nqeK1fvx4fffQR3NzcYGVlhbS0tELXKD59+jRat24NS0tLeHl5YeXKlRrbVd9DEhISNNLzlllYbAWtcbt//371dxYHBwe8/vrr+PvvvzXyqPrntWvXEB4eDgcHB9jb22PkyJF4+vSpVns6d+6MI0eO4PHjxwW8I0T0KouJiYGjoyN69OiBfv36ISYmRr0tOzsbTk5OGDlypNZ+aWlpkMlkmDZtmjotKysLs2bNgre3N6RSKTw8PPDuu+8iKytLY1+JRIIJEyYgJiYGfn5+kEql2LVrFwDgiy++QOvWreHs7AxLS0sEBATku57os2fPMGnSJFSpUgW2trbo3bs37t69m+9a6LqMoYV59uwZtm7dikGDBmHAgAF49uyZXucsec8PS5Lv+PHj2L17N8aOHas1aavSrFkzDB06VCNN3/dm27ZtaNCggfp4qd6fl5X0uOpCn/NbXb6P3Lx5E71794a1tTVcXFzwn//8B7t379Z5fFZRKpVFftfS9RyeDIdX3JJBHTt2DADQtGnTfLdnZmbi0aNHGmm2traQSqVlHhvwYpkEb29vvdZRunTpEgIDA+Hm5ob3338f1tbW+OmnnxAaGorNmzfjjTfe0Mg/ceJEODo6YtasWUhISMCSJUswYcIEbNiwQZ1n3bp1CAsLQ0hICObPn4+nT58iOjoabdq0wdmzZ/M9YS/K9evXAQDOzs4a6f3794ePjw8+/fRT9UnovHnzMHPmTAwYMACjR49GUlISli1bhrZt2+Ls2bNwcHDA8+fPERISgqysLEycOBGurq64e/cuduzYgZSUFNjb2+tclkpycjK6deuGQYMGYdiwYahatSoCAgIwYsQInDx5Un2iDrwY3E6cOIEFCxao03St69tvv8W4cePQunVrTJkyBTdu3EDv3r3h5OSkNSkPAAEBAdi+fTvS0tJgZ2en97EnIsrL2MdDXTx8+BAtW7ZUn0zJ5XLs3LkTb775JtLS0rRu4/vss89gYmKCadOmITU1FZ9//jmGDh2qsW5sdHQ0JkyYgKCgIPznP/9BQkICQkND4ejoCHd3dwBAvXr1MGfOHHz88ccYO3YsgoKCAACtW7dWl/PkyRN07doVffr0wYABA7Bp0ya899578Pf3L/Q20qLY2NjgjTfewLfffovLly/Dz88v33zjx4/Hpk2bMGHCBNSvXx/Jyck4cuQI/v77bzRt2hQffvghUlNTcefOHSxevFhd9ss++eQTWFhYYNq0acjKyip0eYQnT56ge/fuGDBgAAYPHoyffvoJb731FiwsLPS+YlWX2F62d+9edOvWDbVq1cLs2bPx7NkzLFu2DIGBgThz5ozWd5YBAwbAy8sLUVFROHPmDL755hu4uLhg/vz5GvkCAgIghMCxY8eMer1BIiobMTEx6NOnDywsLDB48GBER0erzwfMzc3xxhtvYMuWLVi1apXG5+O2bduQlZWFQYMGAXgxkdW7d28cOXIEY8eORb169XDhwgUsXrwY//77L7Zt26ZR7/79+/HTTz9hwoQJqFKlivozbOnSpejduzeGDh2K58+fY/369ejfvz927NiBHj16qPcPDw/HTz/9hOHDh6Nly5Y4dOiQxnYVfcfQ/Pz8889IT0/HoEGD4OrqiuDgYMTExGDIkCE6HeOCzg+Lk++XX34BAL3uFtL3vTly5Ai2bNmCt99+G7a2tvjyyy/Rt29f3Lp1Sx1baRzXouhzfqvL95GMjAx06NAB9+/fx+TJk+Hq6orY2FgcOHBAo15dxueivmvpeg5PBiaIDOijjz4SAIRCodDaBiDfvzVr1gghhAgLCxOenp5a+82aNUvk7dqenp4iLCxM/frAgQMCgDhw4ECBsaWmpgoAIjQ0VGvbkydPRFJSkvrv6dOn6m0dO3YU/v7+IjMzU52mVCpF69athY+PjzptzZo1AoDo1KmTUCqV6vT//Oc/wtTUVKSkpAghhFAoFMLBwUGMGTNGI4YHDx4Ie3t7rfS8VG397rvvRFJSkrh375749ddfRc2aNYVEIhEnT54UQvzvuA0ePFhj/4SEBGFqairmzZunkX7hwgVhZmamTj979qwAIDZu3FhgLLqWJYQQ7dq1EwDEypUrNfKmpqYKqVQq3nnnHY30zz//XEgkEnHz5k296nr+/LlwcXERjRs3FllZWep8q1evFgBEu3bttNoRGxsrAIg//vijwLYSEenDmMdDIf43ZqnGjPy8+eabolq1auLRo0ca6YMGDRL29vbqsVJVZ7169TQ+d5cuXSoAiAsXLgghhMjKyhLOzs6iefPmIjs7W51v7dq1Wp/PJ0+e1DgmL1ONJz/88IM6LSsrS7i6uoq+ffsW2m4hXhyzHj16FLh98eLFAoDYvn27Og2AmDVrlvq1vb29iIiIKLSeHj165Ps+qo5XrVq1NL5vvLzt5fdP1d6FCxeq07KyskTjxo2Fi4uLeP78uRDif+9pfHx8kWUWFFt8fLzWcVfVk5ycrE47f/68MDExESNGjFCnqfrnqFGjNMp84403hLOzs1Zd9+7dEwDE/PnztbYR0avt1KlTAoCIi4sTQrw4t3J3dxeTJ09W59m9e7cAIH755ReNfbt37y5q1aqlfr1u3TphYmIifv/9d418K1euFADE0aNH1WkAhImJibh06ZJWTHk/j58/fy4aNGggOnTooE47ffq0ACCmTJmikTc8PFxrnNB1DC1Mz549RWBgoPr16tWrhZmZmUhMTNTIp/r8vXLlikhKShLx8fFi1apVQiqViqpVq4qMjAwhxP/Gib1794qkpCRx+/ZtsX79euHs7CwsLS3FnTt3CozljTfeEADU57Qqz5490ziPfvLkiXqbvu+NhYWFuHbtmjrt/PnzAoBYtmyZOq2kx7Wo7wDFOb8t6vvIwoULBQCxbds2ddqzZ8+Er6+vzuOzrt+1dDmHJ8PjUglkUMnJyTAzMyvwyo3XX38dcXFxGn8hISHlEpvqiZr5xRYcHAy5XK7+++qrrwAAjx8/xv79+zFgwAAoFAo8evQIjx49QnJyMkJCQnD16lXcvXtXo6yxY8dq3MoaFBSE3Nxc3Lx5EwAQFxeHlJQUDB48WF3eo0ePYGpqihYtWmj98laQUaNGQS6Xo3r16ujRowcyMjLw/fffay0tMH78eI3XW7ZsgVKpxIABAzTqd3V1hY+Pj7p+1a9xu3fvzvcWR33KUpFKpVq3PNnZ2aFbt2746aefNG5L3bBhA1q2bIkaNWroVdepU6eQmJiI8ePHa/w6Hx4eXuAvjI6OjgCgdfUbEVFxGfN4qAshBDZv3oxevXpBCKHxuRsSEoLU1FT1sgAqI0eO1PjcVV0pe+PGDQAvPp+Tk5MxZswYjQd9DR06VP05rCsbGxuNq34sLCzw2muvqesqCdV7plAoCszj4OCAP/74A/fu3St2PWFhYTo/kM7MzAzjxo1Tv7awsMC4ceOQmJiI06dPFzuGoty/fx/nzp1DeHg4nJyc1OkNGzZE586d8dtvv2ntk/d7R1BQEJKTkzWebA5w7CWqzGJiYlC1alW0b98ewIvb5AcOHIj169cjNzcXwIvb96tUqaJx1+KTJ08QFxeHgQMHqtM2btyIevXqwdfXV2OsUt3+n/d8pF27dqhfv75WTC9/Hj958gSpqakICgrSGOtUt+2//fbbGvtOnDhR43VxxtC8kpOTsXv3bo01Z/v27atemig/devWhVwuh5eXF8aNGwdvb2/8+uuvWs8U6dSpE+RyOTw8PDBo0CDY2Nhg69atcHNzKzCegs6lV65cqXEe/fLSfPq+N506ddJ4WHTDhg1hZ2enHttL47gWRd/zW12+j+zatQtubm7o3bu3Ok0mk2HMmDF6x1fUdy1dzuHJ8LhUAhk1d3f3Yq33VxpsbW0BAOnp6VrbVq1aBYVCgYcPH2p88F67dg1CCMycObPAp2MmJiZqDHKqiUYV1YnJkydPALxYcwb431pCeel6q/7HH3+MoKAgmJqaokqVKqhXr16+T7z28vLSeH316lUIIeDj45NvuaonSXt5eWHq1KlYtGgRYmJiEBQUhN69e2PYsGHqAUHXslTc3NzyvRV04MCB2LZtG44fP47WrVvj+vXrOH36NJYsWaJ33KoJ8rz5zM3NC1xLVzVhXN5rRxJR5WXI8VAXSUlJSElJwerVq7F69ep88yQmJmq8Lmr8U30+531quJmZmd5LBLm7u2t9Zjs6OuKvv/7Sq5z8qL4nqL435Ofzzz9HWFgYPDw8EBAQgO7du2PEiBEFjjP5yTs+F6Z69epaD6OpU6cOgBfr0rZs2VLnsvShes/q1q2rta1evXrYvXu31oNyCusHL3/H4dhLxu7w4cNYsGABTp8+jfv372Pr1q0IDQ3Vef+EhIR8/50fP368zP7NVgS5ublYv3492rdvr7GmaosWLbBw4ULs27cPXbp0gZmZGfr27YvY2FhkZWVBKpViy5YtyM7O1pi4vXr1Kv7++2/I5fJ868s7VhX02btjxw7MnTsX586d01h/9eXPqJs3b8LExESrjLzjWnHG0Lw2bNiA7OxsNGnSRGMN0xYtWiAmJgYRERFa+2zevBl2dnYwNzeHu7u7xiToy7766ivUqVMHZmZmqFq1KurWrVvkw7lfPpd++WKYvn37okGDBgCAd955Rz3xDuj/3uQdP4AXY4jqe0RpHNei6Ht+q8v3kZs3b6J27dpa+fL2G10U9V1Ll3N4MjxO3JJBOTs7IycnBwqFotATnvwU9MX95Q//krC3t0e1atVw8eJFrW2qNW/zPtBDqVQCAKZNm1bglVB5P3BNTU3zzac6QVGVuW7dOri6umrly2/yNT/+/v46nfTnvZpHqVRCIpFg586d+cb68q+oCxcuRHh4OLZv3449e/Zg0qRJiIqKwokTJ+Du7q5XWfnFotKrVy9YWVnhp59+QuvWrfHTTz/BxMQE/fv3L1bc+lINdKqnsBIRlZQxj4e6UI1Vw4YNQ1hYWL55GjZsqPG6qPGvNJVlXarvCYWdUA0YMABBQUHYunUr9uzZgwULFmD+/PnYsmWLzmvs6nq1ra6Mod8Aur83HHvJ2GVkZKBRo0YYNWpUgQ9j0sXevXs11ssuar3RV93+/ftx//59rF+/HuvXr9faHhMTgy5dugAABg0ahFWrVmHnzp0IDQ3FTz/9BF9fXzRq1EidX6lUwt/fH4sWLcq3vrzPt8jvs/f3339H79690bZtW6xYsQLVqlWDubk51qxZg9jYWL3bWJwxNC/Vw9oCAwPz3X7jxg2tHwvbtm2r02fqa6+9pnWXZlF8fX0BvBgjX47Jw8NDfYwdHR017qLQ973R9Ty6JMe1KPqec5bndx9d6yvqHJ4MjxO3ZFCqD/T4+Hi9PzQdHR2RkpKila662qM09OjRA9988w3+/PNPvPbaa0XmVw2G5ubmpXZllOqXTxcXF4NcbVW7dm0IIeDl5aW+Wqcw/v7+8Pf3x0cffYRjx44hMDAQK1euxNy5c/UuqyDW1tbo2bMnNm7ciEWLFmHDhg0ICgpC9erV9Y7b09MTwItfS1++qjk7Oxvx8fEaX/RU4uPjYWJiUqI2EBG9zNjHw6LI5XLY2toiNze31MYq1efztWvX1LfHAkBOTg4SEhI0jpOhrsJMT0/H1q1b4eHhgXr16hWat1q1anj77bfx9ttvIzExEU2bNsW8efPUE7el2YZ79+5pXdn677//AoD6amXVVTd5+05+/UbX2FTv2ZUrV7S2/fPPP6hSpYrWlcC6Ul1pV9RxJjKUbt26FfpDTFZWFj788EP897//RUpKCho0aID58+cjODhYI5+zs3O+F2tUVjExMXBxcVEvTfeyLVu2YOvWrVi5ciUsLS3Rtm1bVKtWDRs2bECbNm2wf/9+fPjhhxr71K5dG+fPn0fHjh2L/bm7efNmyGQy7N69W+MhoWvWrNHI5+npCaVSifj4eI0rMl++IhYo+RgaHx+PY8eOYcKECWjXrp3GNqVSieHDhyM2NhYfffSR3mUXV8+ePfHZZ58hJiamwMnkvErjvXlZWXw3yau0zm9f5unpicuXL0MIoXEc8vYboPS+OxR2Dk+GxzVuyaBatWoF4MU6dvqqXbs2UlNTNW4rUN2WVFreffddWFlZYdSoUXj48KHW9ry/jLm4uCA4OBirVq3C/fv3tfInJSXpHUNISAjs7Ozw6aefIjs7u1TK1EefPn1gamqKyMhIrfYKIZCcnAzgxTpGOTk5Gtv9/f1hYmKivn1I17J0MXDgQNy7dw/ffPMNzp8/r3ELlD51NWvWDHK5HCtXrsTz58/VedauXZvvRAgAnD59Gn5+frx9hIhKjbGPh0UxNTVF3759sXnz5nzvVCnOWNWsWTM4Ozvj66+/1hhfYmJi1FdfqqgmAwv63C4Lz549w/Dhw/H48WN8+OGHhV7BmpqaqpHm4uKC6tWra9xea21trZWvuHJycrBq1Sr16+fPn2PVqlWQy+UICAgA8L8fhg8fPqwRa363k+oaW7Vq1dC4cWN8//33Gu/FxYsXsWfPHnTv3r24TcLp06chkUjU/1aIKpoJEybg+PHjWL9+Pf766y/0798fXbt2VS+LptK7d2+4uLigTZs2+Pnnnw0UrXF49uwZtmzZgp49e6Jfv35afxMmTIBCoVAfJxMTE/Tr1w+//PIL1q1bh5ycHK1zhAEDBuDu3bv4+uuv860vIyOjyLhMTU0hkUg07lBISEjAtm3bNPKp7sBcsWKFRvqyZcu0yivJGKq62vbdd9/VOkYDBgxAu3bt1HnKS2BgIDp37ozVq1dj+/bt+ebJe45WGu/Ny8riu0lepXl+qxISEoK7d+9q/PvPzMzM97iU9LuDLufwZHi84pYMqlatWmjQoAH27t2LUaNG6bXvoEGD8N577+GNN97ApEmT8PTpU0RHR6NOnTolXmRcxcfHB7GxsRg8eDDq1q2LoUOHolGjRhBCID4+HrGxsTAxMdG4heCrr75CmzZt4O/vjzFjxqBWrVp4+PAhjh8/jjt37uD8+fN6xWBnZ4fo6GgMHz4cTZs2xaBBgyCXy3Hr1i38+uuvCAwMxPLly0ulvfmpXbs25s6dixkzZiAhIQGhoaGwtbVFfHw8tm7dirFjx2LatGnYv38/JkyYgP79+6NOnTrIycnBunXr1AOmPmXponv37rC1tcW0adM06tA3bnNzc8ydOxfjxo1Dhw4dMHDgQMTHx2PNmjX5rj2YnZ2NQ4cOaT1kgIioJIx9PFT57rvv1A9bednkyZPx2Wef4cCBA2jRogXGjBmD+vXr4/Hjxzhz5gz27t2Lx48f61WXhYUFZs+ejYkTJ6JDhw4YMGAAEhISsHbtWq2132rXrg0HBwesXLkStra2sLa2RosWLfRaF7Ywd+/exY8//gjgxVW2ly9fxsaNG/HgwQO88847Gg8Cy0uhUMDd3R39+vVDo0aNYGNjg7179+LkyZNYuHChOl9AQAA2bNiAqVOnonnz5rCxsUGvXr2KFW/16tUxf/58JCQkoE6dOtiwYQPOnTuH1atXq9fb8/PzQ8uWLTFjxgw8fvwYTk5OWL9+vdYJnL6xLViwAN26dUOrVq3w5ptv4tmzZ1i2bBns7e0xe/bsYrUHePGw1sDAwEp/2zhVTLdu3cKaNWtw69Yt9R1i06ZNw65du7BmzRp8+umnsLGxwcKFCxEYGAgTExNs3rwZoaGh2LZtm8ZDiiqTn3/+GQqFosD2t2zZEnK5HDExMeoJ2oEDB2LZsmWYNWsW/P39ta7SHz58OH766SeMHz8eBw4cQGBgIHJzc/HPP//gp59+wu7du4tcFqBHjx5YtGgRunbtiiFDhiAxMRFfffUVvL29NX5EDQgIQN++fbFkyRIkJyejZcuWOHTokPoOiJfHsZKMoTExMWjcuLHWUgIqvXv3xsSJE3HmzBk0bdq00LaVph9//BFdu3ZFaGgounXrhk6dOsHR0REPHjzA3r17cfjwYY2r1EvjvcmrNL6bXLt2Ld8rT5s0aYIePXqU2vmtyrhx47B8+XIMHjwYkydPRrVq1RATEwOZTAZAs9+U9LuDLufwZAQEkYEtWrRI2NjYiKdPn2qkAxARERGF7rtnzx7RoEEDYWFhIerWrSt+/PFHMWvWLJG3a3t6eoqwsDD16wMHDggA4sCBAzrFeO3aNfHWW28Jb29vIZPJhKWlpfD19RXjx48X586d08p//fp1MWLECOHq6irMzc2Fm5ub6Nmzp9i0aZM6z5o1awQAcfLkSY19C4rtwIEDIiQkRNjb2wuZTCZq164twsPDxalTpwqNXVXexo0bC82nOm5JSUn5bt+8ebNo06aNsLa2FtbW1sLX11dERESIK1euCCGEuHHjhhg1apSoXbu2kMlkwsnJSbRv317s3btX77KEEKJdu3bCz8+v0JiHDh0qAIhOnToVmEeXuoQQYsWKFcLLy0tIpVLRrFkzcfjwYdGuXTvRrl07jXw7d+4UAMTVq1cLjY2ISF/GPB6qxqyC/m7fvi2EEOLhw4ciIiJCeHh4CHNzc+Hq6io6duwoVq9erVVn3nEpPj5eABBr1qzRSP/yyy+Fp6enkEql4rXXXhNHjx4VAQEBomvXrhr5tm/fLurXry/MzMw0yiloPAkLCxOenp6FtluIF8dM1U6JRCLs7OyEn5+fGDNmjPjjjz/y3QeAmDVrlhBCiKysLDF9+nTRqFEjYWtrK6ytrUWjRo3EihUrNPZJT08XQ4YMEQ4ODgKAOrbCxvH83j9Ve0+dOiVatWolZDKZ8PT0FMuXL9fa//r166JTp05CKpWKqlWrig8++EDExcVplVlQbAW9Z3v37hWBgYHC0tJS2NnZiV69eonLly9r5Cnoe4eqr8XHx6vTUlJShIWFhfjmm2/yOdpExgeA2Lp1q/r1jh07BAD191HVn5mZmRgwYECB5QwfPly0adOmHCI2Tr169RIymUxkZGQUmCc8PFyYm5uLR48eCSGEUCqVwsPDQwAQc+fOzXef58+fi/nz5ws/Pz8hlUqFo6OjCAgIEJGRkSI1NVWdr7Dx99tvvxU+Pj5CKpUKX19fsWbNmnzH3YyMDBERESGcnJyEjY2NCA0NFVeuXBEAxGeffaaRV5cxNK/Tp08LAGLmzJkF5klISBAAxH/+8x8hRNHnfSoFna/q49mzZ2LJkiWiVatWws7OTpiZmQlXV1fRs2dPERMTI3JycjTyl/S9yfs9R4jiHdeXyyvou8+bb76pzleS89v8vo/cuHFD9OjRQ1haWgq5XC7eeecdsXnzZgFAnDhxQp1P3+8Oecdtfc7hyXAkQpTRKshEOkpNTUWtWrXw+eef48033zR0OESFCg0NhUQiKddbkImocuB4qBulUgm5XI4+ffrke9sgvXqWLFmCzz//HNevXy/1h7QRlQXVd8XQ0FAAwIYNGzB06FBcunRJ62FBNjY2Ba5p+9VXX2Hu3Ln5LsFGFde5c+fQpEkT/Pjjjxg6dKihw6EKYsmSJfjPf/6DO3fuwM3NzdDhUDniGrdkcPb29nj33XexYMEC9ZMfiYzR33//jR07duCTTz4xdChE9ArieKgtMzNTa824H374AY8fP9Z6oA+9mrKzs7Fo0SJ89NFHnLSlCqtJkybIzc1FYmIivL29Nf4KexDZuXPnUK1atXKMlErbs2fPtNKWLFkCExMTtG3b1gARUUWQt99kZmZi1apV8PHx4aRtJcQrbomIiIjIKB08eBD/+c9/0L9/fzg7O+PMmTP49ttvUa9ePZw+fRoWFhaGDpGICMCL9a9VT31v0qQJFi1ahPbt28PJyQk1atTAsGHDcPToUSxcuBBNmjRBUlIS9u3bh4YNG6JHjx74/vvvYWFhgSZNmgAAtmzZgpkzZ+Kbb77ByJEjDdk0KoHIyEicPn0a7du3h5mZGXbu3ImdO3di7NixGg+RJHpZt27dUKNGDTRu3Bipqan48ccfcenSJcTExGDIkCGGDo/KGSduiYiIiMgoJSQkYNKkSfjzzz/VD9Dq3r07PvvsM7i4uBg6PCIitYMHD6J9+/Za6WFhYVi7di2ys7Mxd+5c/PDDD7h79y6qVKmCli1bIjIyEv7+/vj+++8xf/583Lx5E2ZmZvD19cX06dPRr18/A7SGSktcXBwiIyNx+fJlpKeno0aNGhg+fDg+/PBDmJnxWfGUvyVLluCbb75BQkICcnNzUb9+fbz77rvqh/BR5cKJWyIiIiIiIiIiIiIjwzVuiYiIiIiIiIiIiIxMhb42X6lU4t69e7C1tYVEIjF0OEREVEEIIaBQKFC9enWYmFTe3zA5jhIRUXFwHH2B4ygRERWHPuNohZ64vXfvHjw8PAwdBhERVVC3b9+Gu7u7ocMwGI6jRERUEhxHOY4SEVHx6TKOVuiJW1tbWwAvGmpnZ1fscpRKJZKSkiCXyyvNL8aVrc2Vrb0A21wZ2lzZ2guUXpvT0tLg4eGhHkcqK46jxcc2v/ptrmztBSpfmytbewGOo6WttMZRIiKqXPQZRyv0xK3qdhQ7O7sSn3BmZmbCzs6uUn1pq0xtrmztBdjmytDmytZeoPTbXNlva+Q4Wnxs86vf5srWXqDytbmytRfgOFraSmscJSKiykmXcbRyfEMhIiIiIiKiV1Z0dDQaNmyonkRt1aoVdu7cWeg+GzduhK+vL2QyGfz9/fHbb7+VU7TlZ+HChQgODka1atUglUrh6emJsLAw3LhxQ50nISEB4eHh8PT0hEwmQ926dfH5559DqVRqlHXt2jX069cPTk5OsLS0RNOmTbFhw4YiYzh48CAkEkm+f3v37lXHUFAeiUSC2bNnq8u7cOEC+vbtCzc3N8hkMjRs2BBr1qwpnQP2inoV+8GZM2cQGhqK6tWrQyqVomrVqujWrRt+//330jlorxj2gYqrQl9xS0REREREROTu7o7PPvsMPj4+EELg+++/x+uvv46zZ8/Cz89PK/+xY8cwePBgREVFoWfPnoiNjUVoaCjOnDmDBg0aGKAFZWPZsmW4desW6tatC0tLS8THx+OHH37Anj17cOXKFWRlZeG1115DUlISbGxs4Ovri4sXL+K9997DvXv3sGTJEgDA/fv3ERgYiMTERNjZ2aFatWo4e/YsBg0ahIyMDIwaNarIWCwsLNCkSRONNHt7ewCAVCpFixYtNLalpKTgypUrAIBq1aoBAC5fvoyWLVvi6dOncHJygo+PDy5cuIBRo0YhNTUVU6ZMKeERezW9av0gJSUFHTt2REpKCmxsbODn54crV65g165dOHDgAG7fvg25XF7Sw/ZKYR+owEQFlpqaKgCI1NTUEpWTm5sr7t+/L3Jzc0spMuNX2dpc2dorBNtcGVS29gpRem0urfGjouM4Wnxs86uvsrVXiMrX5srWXiEq3zjq6Ogovvnmm3y3DRgwQPTo0UMjrUWLFmLcuHE6l18RjsPcuXPFzZs31a+nTJkiAAgAYsuWLeKrr75Sv7548aIQQojVq1cLAMLU1FTcunVLCCHExIkTBQBha2sr7t69K4QQom/fvgKAqFKlisjKyiowhgMHDggAwtPTU6/YIyIiBADh6OgoFAqFEEKI6dOnCwBCKpWK5ORkIYQQH3zwgQAg7O3txdOnT/Wqo7J41frB77//ro53/fr1QgghvvvuO3XahQsX9KqjMmAfMC76jB+84paIiIiIiIotNzcX2dnZhg5Db0qlEtnZ2cjMzKxUa9zq0mZzc3OYmpqWY2SlKzc3Fxs3bkRGRgZatWqVb57jx49j6tSpGmkhISHYtm1bgeVmZWUhKytL/TotLQ3Ai+Oa91ZiYzFjxgwAUMcXGBiovnLO3Nwcubm5Gvlfbkdubi727duHESNGqJedaNmyJVxdXaFUKhEaGorNmzfj0aNH+PPPP9G6det8Y1CVee/ePTg4OAAAfH19MXXqVPTr1y/ffZKTk9XLH4wfPx5WVlZQKpUa8QohoFQq1WtEpqam4o8//kDbtm11Pj6VxavWD+rVqwdHR0c8efIEo0ePxueff45//vkHlpaWmDJlCurXr2+0/yYNhX3AuOgTGyduiYiIiIhIb0IIPHjwACkpKYYOpVhUkz4KhaLSPGRLnzY7ODjA1dW1Qh2bCxcuoFWrVsjMzISNjQ22bt2K+vXr55v3wYMHqFq1qkZa1apV8eDBgwLLj4qKQmRkpFZ6UlISMjMzSxZ8OcjNzcVXX30FAPD09IS/vz/kcjmsra2RkZGBli1bwtPTU31LMgD8+++/SExMxO3btwG8eBBbYmIigBe3NKtcvHgR3t7e+dar+oyoUqUKHB0dcf36dfzxxx8YOHAgPvvsM4SFhWnts2jRIjx9+hRSqRSDBg1S1xkcHIylS5ciKysLPj4+cHV1xT///KPe7++//4avr28JjtKr71XoBwCwdetWhIWF4ebNmzhz5gyAF0umeHl5aeQjbewDhqdQKHTOy4lbIiIiIiLSm2rS1sXFBVZWVhVqgg94MYmZk5MDMzOzChd7cenSZiEEnj59qj7pVa0nWBHUrVsX586dQ2pqKjZt2oSwsDAcOnSowMlbfc2YMUPjKt20tDR4eHhALpfDzs6uVOooKxkZGRgyZAgOHjwIV1dX7NixAx4eHvDw8MDu3bsxc+ZMnDlzBomJiQgLC8N3330HIQQcHR3h4uKiLkcmk6lfq9akBF5M4ryc72WBgYH4999/Ubt2bQDArVu30LJlSzx8+BBff/01pk+frpE/KysLP/zwAwBg6NChGmsO9+jRA1u3bsW8efNw6dIlpKamYvjw4er8zs7OBcZBr04/yMjIwLRp03Dz5k0sWLAA48aNw+rVqzFt2jSMGzcOTZs21VpDlV5gHzAOMplM98xltmBDOeDafMVX2dpc2dorBNtcGVS29gpR+dbmK2scR4uPbX71Vbb2CqFfm3NycsTly5fFo0ePyiGysqFUKsXz58+FUqk0dCjlRp82P3r0SFy+fFnk5ORobaso42jHjh3F2LFj893m4eEhFi9erJH28ccfi4YNG+pcfkU5Dvfv3xcBAQECgKhTp464fv16ofmPHTumXify559/FkII4e3tLQCILl26qPPFxMSo8x09elSvmFRrYpqbm2ttU62rKZFIxOXLl4ssKzY2Vh3HX3/9pVcclcmr1A9U2wCIx48fCyGEePz4sTptwYIFesVRWbAPGA99xo/KsZgTERERERGVGtWatlZWVgaOhMqK6r2tiOsXqyiVSo01aV/WqlUr7Nu3TyMtLi6uwDVxK6pLly6hZcuWOH36NIKCgnD8+HHUqlVLI8+RI0fU61s+efIE06ZNA/DiduaOHTsCALp27QrgxdrA9+7dAwBs2bJFna9Zs2YAgPDwcEgkEtSsWVNd/g8//IA//vhD/frOnTs4cuQIAGjkA15c8b1w4UIAL66urVevnlabDh06pP7v27dvY/bs2QAAPz8/jSvy6H9etX6Qmpqq/u9Tp05p/H8AsLa21um4VCbsAxVYWc8ilyVeKVR8la3Nla29QrDNlUFla68QvOK2tHEcLT62+dVX2dorhH5tfvbsmbh8+bJ49uxZOURWNnjFbeEKe4+NcRx9//33xaFDh0R8fLz466+/xPvvvy8kEonYs2ePEEKI4cOHi/fff1+d/+jRo8LMzEx88cUX4u+//xazZs0S5ubmej2J3BiPQ1516tRRX4HWuHFj0aJFC/Xf119/LYQQws/PTzg4OAh/f39hZWWlfor8pk2b1OXcuXNHVKlSRQAQdnZ2wsvLS13u6tWr1fnCwsK0nhqvSqtSpYpo2LChkMlk6n3Xrl2rEe/27dvV2w4dOpRvm6ytrYVcLhcNGjQQUqlUABBWVlbi2LFjpXjkXi2vWj/4+++/hYWFhQAgLCwshL+/v7ov2Nvbi3v37pXyEaz42AeMC6+4JSIiIiIiokojMTERI0aMQN26ddGxY0ecPHkSu3fvRufOnQG8WEvx/v376vytW7dGbGwsVq9ejUaNGmHTpk3Ytm3bK3fF5stXHJ87dw5//PGH+u/OnTsAgC5dusDOzg5XrlyBmZkZunTpgv3796Nv377qfd3c3HD06FH06dMHEokE9+7dQ+PGjRETE4MxY8ao8z158gQA4O/vr04bPnw4+vfvDxsbG/z777+wt7dHp06dEBcXp/Uwoi+++AIA8Nprr6Ft27b5tqlXr14wMzPDlStXYG1tjT59+uD48eOv3NXSpelV6we+vr44dOgQXn/9dVSpUgVXrlyBXC7HwIEDcezYsQq1Nnd5YR+ouCRCCGHoIIorLS0N9vb2SE1NLdFi8EqlEomJiXBxcYGJSeWYy65sba5s7QXY5srQ5srWXqD02lxa40dFx3G0+NjmV7/Nla29gH5tzszMRHx8PLy8vDQesJGUlIS0tLSyDlXNzs4Ocrm8WPsKPpys0LwFvccAx1EVHgdNQgjI5XI8ffoUly5dgpeXl6FDIgNgPyD2gaLpM36YlVNMRERERET0CktKSsKQkUOQrEgutzqdbZ0Ruya22JO3ZengwYNo3749njx5AgcHhwLz1axZE1OmTMGUKVPKLTaisnDx4kUkJycjKiqKEzWVGPsBsQ+ULoNO3M6ePRuRkZEaaXXr1sU///xjoIiIiIgMIzo6GtHR0UhISADw4gEbH3/8Mbp161bgPhs3bsTMmTORkJAAHx8fzJ8/H927dy+niImINKWlpSFZkQxpWyksnS3LvL5nyc+QfDgZaWlpek3choeHIyUlBVu3btVI13WitbjWrl2LKVOmICUlpdTLJjIG/v7+qMA39FIpYT8g9oHSZfArbv38/LB37171azMzg4dERERU7tzd3fHZZ5/Bx8cHQgh8//33eP3113H27Fn4+flp5T927BgGDx6MqKgo9OzZE7GxsQgNDcWZM2deufX5iKhisXS2hHXV8nmacxayis5EREREVEEZfJbUzMwMrq6uOuXNysrSWFBZtX6WUqmEUqksdgxKpRIpKSlQKBRlur6VnZ0dqlSpUmbl60OpVEIIUaLjVpFUtvYCbHNlUNnaC5Rem43xmPXq1Uvj9bx58xAdHY0TJ07kO3G7dOlSdO3aFdOnTwcAfPLJJ4iLi8Py5cuxcuXKfOvgOFp6+O/v1VfZ2gvo12ZVXtUfgP9dXaN6DnRZU1X3Ugx6F/FS7Hn/+8iRI/jggw9w6tQpVKlSBaGhoYiKioK19YtJ6XXr1uHLL79UPyCpQ4cOWLx4MVxcXLTKPnDgAEaOHAkA6s/Jjz/+GLNnzwYAZGRkYOTIkdi0aRMcHR3x4YcfYuzYsQCAjh07ol69eli+fLk67qSkJLi7u+O3335Dx44di93movKp+kPePlGZ/l2Ul/JeH5r0U5L1tPXBfmDc2A8IKL9+oGLwidurV6+ievXqkMlkaNWqFaKiolCjRo1880ZFRWktrQC86NSZmZnFjiElJQXrN/yEW/cfAGV4ObelhQUmTXgb9vb2ZVaHrpRKJVJTUyGEqBQP3Khs7QXY5srQ5srWXqD02qxQKEoxqtKXm5uLjRs3IiMjo8AnJB8/fhxTp07VSAsJCcG2bdsKLLcsx9GN69bhwY0bZTqOmtva4q1p0ziOGkhla3Nlay+gX5uzs7OhVCqRk5ODnJwcAEBOTg4EBJRCCaUo+4k9pVBCQGjEoNN+/z8RmZ2djdzcXAAvJlNV/52Tk4MrV66gW7duiIyMxKpVq/Do0SNMnjwZERER+OabbwC8+DFs1qxZqFOnDpKSkjB9+nSEh4fj559/BgCN8l577TUsXLgQkZGRuHjxIgDAxsZGHfeiRYswe/ZsvPvuu9iyZQvefvttBAYGom7duggPD8eUKVMwf/58SKVSAMAPP/wANzc3tG3bVq+2CyE02lyYnJwcKJVKJCcnw9zcXGObsY+jFY0h1ocm/ZTHetpJSUl4a8gQZCWzHxgrqbMzomPLvh8MGfIWkpN5R4mxcnaWIjY2utwmbw06cduiRQusXbsWdevWxf379xEZGYmgoCBcvHgRtra2WvlnzJihcZKalpYGDw8PyOXyEj3FU6FQ4Pb9B6gR1BXOrh7FLqcwyQ/u4NCPq2Bqaqr+Fd6QlEolJBIJ5HJ5pTgZqWztBdjmytDmytZeoPTanPfp2MbiwoULaNWqFTIzM2FjY4OtW7eifv36+eZ98OABqlatqpFWtWpVPHjwoMDyy3IcfXjjBvreuoUaZXRsbz97hsVZWRxHDaiytbmytRfQr82ZmZlQKBQwMzNTL3VmZmYGCSQwkZjARFL2x8xEYgIJJBox6LSfiQl+++03ODk5aaSrJjTNzMywYMECDBkyROMz88svv0RwcDBWrlwJmUyG0aNHq7fVqVMHX375JV577TX1Z7ipqam6PCsrKzg6OkIikcDd3V0rpu7du2PChAkAXnxWf/nll/j999/h5+eH/v37Y8qUKfj1118xYMAAAC+u9g0LC9OaUNWVLvuZmZnBxMQEzs7OWuOmsY6jFVV5rw9N+inuetr6SktLQ1ZyMt6RSuFhyX5gbG4/e4aFyeXTD5KTsyCVvgNLy7KZn6Lie/bsNpKTF5Z5P3iZQSduX37gSsOGDdGiRQt4enrip59+wptvvqmVXyqVqn9lfpmJiUmJvlBLJBIIIeDs6gHXmrWKXU4RlUAIAYlEYjRf/lWxGEs8Za2ytRdgmyuDytZeoHTabKzHq27dujh37hxSU1OxadMmhIWF4dChQwVO3uqrrMfRGjIZvK2sShJiwXUIAZGZyXHUwCpbmytbewHd22xiYgKJRKL+U+374j/+/6+sqap7KQZdtW/fHitWrEBOTs6LCWeJBH/88QeGDRsGiUSCv/76C3/99RdiY2PV+6iWDUhISEC9evVw+vRpzJ49G+fPn8eTJ0/Uywfcvn0b9evX1zgu+R6nlzRs2FBju6urK5KSkiCRSGBpaYnhw4djzZo1GDhwIM6cOYOLFy/i559/1rvdqvORguJ4mSrm/PpDZfo3UZ7Kc31o0k95rqftYWmJ2tbsB0Ypq/z6gaWlB6yta5dbfaS7cuwGAIxgqYSXOTg4oE6dOrh27ZqhQyEiIip3FhYW8Pb2BgAEBATg5MmTWLp0KVatWqWV19XVFQ8fPtRIe/jwoc7rxhMRVWbW1tbw9vbWmLi9c+eOent6ejrGjRuHSZMmae1bo0YNZGRkICQkBCEhIYiJiYFcLsetW7cQEhKC58+f6x1P3itgJRKJxjqyo0ePRuPGjXHnzh2sWbMGHTp0gKenp971EBERUcViVD+Vpqen4/r166hWrZqhQyEiIjI4pVKp8TCxl7Vq1Qr79u3TSIuLiytwTVwiItJd06ZNcfnyZXh7e2v9WVhY4J9//kFycjI+++wzBAUFwdfXF4mJiYWWaWFhoV6OQV/+/v5o1qwZvv76a8TGxmLUqFHFKoeIiIgqFoNecTtt2jT06tULnp6euHfvHmbNmgVTU1MMHjzYkGERERGVuxkzZqBbt26oUaMGFAoFYmNjcfDgQezevRsAMGLECLi5uSEqKgoAMHnyZLRr1w4LFy5Ejx49sH79epw6dQqrV682ZDOIiPAs+VmFr+e9995Dy5YtMWHCBIwePRrW1ta4fPky4uLisHz5ctSoUQMWFhZYtmwZxo8fj4sXL+KTTz4ptMyaNWsiPT0d+/btQ6NGjWBlZQUrPZaXGT16NCZMmABra2u88cYbJW0iERERVQAGnbi9c+cOBg8ejOTkZMjlcrRp0wYnTpwotwV+iYiIjEViYiJGjBiB+/fvw97eHg0bNsTu3bvRuXNnAMCtW7c01hRs3bo1YmNj8dFHH+GDDz6Aj48Ptm3bhgYNGhiqCURUydnZ2cHZ1hnJh5PLbT1IZ1vnEj1csSANGzbEoUOH8OGHHyIoKAhCCNSuXRsDBw4EAMjlcqxduxYffPABvvzySzRt2hRffPEFevfuXWCZrVu3xvjx4zFw4EAkJydj1qxZmD17ts4xDR48GFOmTMHgwYP5cDAiIqJKwqATt+vXrzdk9UREREbj22+/LXT7wYMHtdL69++P/v37l1FERET6kcvliF0Ti7S0tHKr087OTu+LPtauXQvgxYO6XhYcHKyR1rx5c+zZs6fAcgYPHqx1p+DL++ctDwCio6MRHR2tkZaQkKBV9rlz57TSHj16hMzMzHwf4kxERESvJqN6OBkREREREVVccrmcd8+VsuzsbCQnJ+Ojjz5Cy5Yt0bRpU0OHREREROXEqB5ORkRERERERP9z9OhRVKtWDSdPnsTKlSsNHQ4RERGVI15xS0REREREZKTyW3KBiIiIKgdecUtERERERERERERkZDhxS0RERERERERERGRkOHFLREREREREREREZGQ4cUtERERERERERERkZDhxS0RERERERERERGRkzAwdABERERERvRqSkpKQlpZWbvXZ2dlBLpeXW31ERERE5YkTt0REREREVGJJSUl4a8gQZCUnl1udUmdnRMfGGmTytmbNmpgyZQqmTJlSpvVIJBJs3boVoaGhBeYJDw9HSkoKtm3bVqaxEBERUfnixC0REREREZVYWloaspKT8Y5UCg9LyzKv7/azZ1iYnIy0tDS9Jm6Dg4PRuHFjLF68WCN97dq1mDJlClJSUko50tKTkJAALy8vnD17Fo0bNzZ0OERERFTGOHFLRERERESlxsPSErWtrcunsqys8qmHiIiIyAD4cDIiIiIiIqKXhIeHIzQ0FF988QWqVasGZ2dnREREIDs7WyPf06dPMWrUKNja2qJGjRpYvXq1xvbbt29jwIABcHBwgJOTE15//XUkJCSot588eRKdO3dGlSpVYG9vj3bt2uHMmTMFxuXl5QUAaNKkCSQSCYKDgzW2FxTvnDlz0KBBA63yGjdujJkzZ+pzaIiIiKgcceKWiIiIiIgojwMHDuD69es4cOAAvv/+e6xduxZr167VyLNw4UI0a9YMZ8+exdtvv4233noLV65cAQBkZ2cjJCQEtra2+P3333H06FHY2Niga9eueP78OQBAoVAgLCwMR44cwYkTJ+Dj44Pu3btDoVDkG9Off/4JANi7dy/u37+PLVu26BTvqFGj8Pfff+PkyZPq/GfPnsVff/2FkSNHltYhIyIiolLGiVsiIiIiIqI8HB0dsXz5cvj6+qJnz57o0aMH9u3bp5Gne/fuePvtt+Ht7Y333nsPVapUwYEDBwAAGzZsgFKpxDfffAN/f3/Uq1cPa9aswa1bt3Dw4EEAQIcOHTBs2DD4+vqiXr16WL16NZ4+fYpDhw7lG5NqLV9nZ2e4urrCyclJp3jd3d0REhKCNWvWqPOvWbMG7dq1Q61atUrtmBEREVHp4sQtERERERFRHn5+fjA1NVW/rlatGhITEzXyNGzYUP3fEokErq6u6jznz5/HtWvXYGtrCxsbG9jY2MDJyQmZmZm4fv06AODhw4cYM2YMfHx8YG9vDzs7O6Snp+PWrVulHu+YMWOwfv16ZGZm4vnz54iNjcWoUaP0roeIiIjKDx9ORkRERERElYadnR1SU1O10lNSUmBvb69+bW5urrFdIpFAqVRqpBWWJz09HQEBAYiJidGqS3XlbFhYGJKTk7F06VJ4enpCKpWiVatW6qUU9FFUvL169YJUKsW2bdtgaWmJ7Oxs9OvXT+96iIiIqPxw4paIiIiIiCqNunXrYs+ePVrpZ86cQZ06dUqtnqZNm2LDhg1wcXGBnZ1dvnmOHj2KFStWoHv37gBePMzs0aNHBZZpYWEBAMjNzdU7HjMzM4wYMQI//PADpFIpBg0aBEtLS73LISIiovLDiVsiIiIiIio1t589M+p63nrrLSxfvhyTJk1CeHg4rK2t8dtvv+G///0vfvnll1KLb+jQoViwYAFef/11zJkzB+7u7rh58ya2bNmCd999F+7u7vDx8cG6devQrFkzpKWlYfr06YVOprq4uMDS0hK7du2Cu7s7ZDKZxlXCRRk9ejTq168P4MWkMRERERk3TtwSEREREVGJ2dnZQersjIXJyUBWVrnUKXV2LvBq1oLUqlULhw8fxocffohu3brh+fPn8PX1xcaNG9G1a9dSi83KygqHDx/Ge++9hz59+kChUMDNzQ0dO3ZUx/ztt99i7NixaNq0KTw8PPDpp59i2rRpBZZpZmaGL7/8EnPmzMHHH3+MoKAg9YPOdOHj44NWrVrhyZMnaNGiRUmbSERERGWME7dERERERFRicrkc0bGxSEtLK7c67ezs1OvF6qN58+bYvXs3cnJyYGZmBolEorF97dq1WvssWbJE43VCQoJWnnPnzmm8dnV1xffff19gHE2aNMHJkyc10vKuOyuE0Hg9evRojB49Wu94VWXdv38fb731VoExERERkfHgxC0REREREZUKuVxerIlUKntJSUn473//iwcPHmDkyJGGDoeIiIh0wIlbIiIiIiKiV5yLiwuqVKmCFStWwNHR0dDhEBERkQ44cUtERERERPSKE0JACIGcnBxDh0JEREQ6MjF0AEREREREVDHlXX+VXh18b4mIiAyPE7dERERERKQXc3NzAMDTp08NHAmVFdV7q3qviYiIqPxxqQQiIiIiItKLqakpHBwckJiYCACwsrKCRCIxcFT6US0bYGZmVuFiLy5d2iyEwNOnT5GYmAgHBweYmpqWc5RERESkwolbIiIiIiLSm6urKwCoJ28rGiEElEolTExMKtXEra5tdnBwUL/HREREZBicuCUiIiIiIr1JJBJUq1YNLi4uyM7ONnQ4elMqlUhOToazszNMTCrHCnK6ttnc3JxX2hIRERkBTtwSEREREVGxmZqaVshJPqVSCXNzc8hksko1cVvZ2kxERFSRcbQmIiIiIiIiIiIiMjKcuCUiIiIiIiIiIiIyMpy4JSIiIiIiIiIiIjIynLglIiIiIiIiIiIiMjKcuCUiIiIiIiIiIiIyMpy4JSIiIiIiIiIiIjIynLglIiIiIiIiIiIiMjKcuCUiIiIiIiIiIiIyMpy4JSIiIiIiIiIiIjIynLglIiIiIiIiIiIiMjKcuCUiIiIiIqIKLSoqCs2bN4etrS1cXFwQGhqKK1euFLrP2rVrIZFINP5kMlk5RUxERFQ0TtwSERERERFRhXbo0CFERETgxIkTiIuLQ3Z2Nrp06YKMjIxC97Ozs8P9+/fVfzdv3iyniImIiIpmZugAiIiIiIiIiEpi165dGq/Xrl0LFxcXnD59Gm3bti1wP4lEAldXV53qyMrKQlZWlvp1WloaAECpVEKpVBYj6heEEC+u+P3//5FxkeDF1dhCiBK9z0VR9QMhkUApYT8wNkJSvv1AIhGQSMquHiqeF+9LyfuBPvty4paIiIiIiIheKampqQAAJyenQvOlp6fD09MTSqUSTZs2xaeffgo/P79880ZFRSEyMlIrPSkpCZmZmcWOVaFQwMfLB9aW1pCZcqkGY5NpmYkMrwwoFAokJiaWWT0KhQIePj5QWFsjkUt2GB1FZiY8MsqnH/j4eMDaWgGZrOzqoeLJzFQgI8OjxP1AoVDonJcTt0RERERERPTKUCqVmDJlCgIDA9GgQYMC89WtWxffffcdGjZsiNTUVHzxxRdo3bo1Ll26BHd3d638M2bMwNSpU9Wv09LS4OHhAblcDjs7u2LHm56ejqvxV+HQyAHWdtbFLofKRsazDKTEp6jXTy4r6enpuH31KmwdHOBizX5gbNIzMnA7pXz6wdWrt+HgYAtr67Krh4onIyMdKSm3S9wP9FlPnRO3RERERERE9MqIiIjAxYsXceTIkULztWrVCq1atVK/bt26NerVq4dVq1bhk08+0covlUohlUq10k1MTGBiUvzHx6huu1X9j4yLgFDfvl6S97koqn4gEQImgv3A2EhE+fYDISQQgo+lMjYv3peS9wN99uXELREREREREb0SJkyYgB07duDw4cP5XjVbGHNzczRp0gTXrl0ro+iIiIj0w+l7IiIiIiIiqtCEEJgwYQK2bt2K/fv3w8vLS+8ycnNzceHCBVSrVq0MIiQiItIfr7glIiIiIiKiCi0iIgKxsbHYvn07bG1t8eDBAwCAvb09LC0tAQAjRoyAm5sboqKiAABz5sxBy5Yt4e3tjZSUFCxYsAA3b97E6NGjDdYOIiKil3HiloiIiIiIiCq06OhoAEBwcLBG+po1axAeHg4AuHXrlsa6gk+ePMGYMWPw4MEDODo6IiAgAMeOHUP9+vXLK2wiIqJCceKWiIjICERFRWHLli34559/YGlpidatW2P+/PmoW7dugfusXbsWI0eO1EiTSqXIzMws63CJiIiMitDhYU4HDx7UeL148WIsXry4jCIiIiIqOa5xS0REZAQOHTqEiIgInDhxAnFxccjOzkaXLl2QkZFR6H52dna4f/+++u/mzZvlFDERERERERGVJV5xS0REZAR27dql8Xrt2rVwcXHB6dOn0bZt2wL3k0gkcHV11amOrKwsZGVlqV+npaUBAJRKJZRKZTGifkEIAYlEAiGRQCmRFLucQuuQSF7UIUSJYi0tSqXSaGIpL5WtzZWtvUDla3Nlay9Qem2uTMeMiIjIkDhxS0REZIRSU1MBAE5OToXmS09Ph6enJ5RKJZo2bYpPP/0Ufn5++eaNiopCZGSkVnpSUlKJlldIT09HlWrVkG5tjUSptNjlFEaRmQmPjAwoFAokJiaWSR36UCqVSE1NhRBCY73EV1lla3Nlay9Q+dpc2doLlF6bFQpFKUZFREREBeHELRERkZFRKpWYMmUKAgMD0aBBgwLz1a1bF9999x0aNmyI1NRUfPHFF2jdujUuXboEd3d3rfwzZszA1KlT1a/T0tLg4eEBuVwOOzu7YserUCjw6P592CQlwcXKqtjlFCY9IwO3U1Jga2sLFxeXMqlDH0qlEhKJBHK5vFJN+FSmNle29gKVr82Vrb1A6bVZJpOVYlRERERUEE7cEhERGZmIiAhcvHgRR44cKTRfq1at0KpVK/Xr1q1bo169eli1ahU++eQTrfxSqRTSfK6INTExKdEJvGoJA4kQMNHh4TDFqkMI9ZIMxjLBoorFWOIpD5WtzZWtvUDla3Nlay9QOm2uTMeLiIjIkDhxS0REZEQmTJiAHTt24PDhw/leNVsYc3NzNGnSBNeuXSuj6IiIiIiIiKi88KdSIiIiIyCEwIQJE7B161bs378fXl5eepeRm5uLCxcuoFq1amUQIREREREREZUnXnFLRERkBCIiIhAbG4vt27fD1tYWDx48AADY29vD0tISADBixAi4ubkhKioKADBnzhy0bNkS3t7eSElJwYIFC3Dz5k2MHj3aYO0gIiIiIiKi0sGJWyIiIiMQHR0NAAgODtZIX7NmDcLDwwEAt27d0lhX8MmTJxgzZgwePHgAR0dHBAQE4NixY6hfv355hU1ERERERERlhBO3RERERkDo8FCvgwcParxevHgxFi9eXEYRERERERERkSFxjVsiIiIiIiIiIiIiI2M0E7efffYZJBIJpkyZYuhQiIiIiIiIiIiIiAzKKCZuT548iVWrVqFhw4aGDoWIiIiIiIiIiIjI4Ay+xm16ejqGDh2Kr7/+GnPnzi00b1ZWFrKystSv09LSAABKpRJKpbLYMQghIJFIACEAUfxyiqgEEokEQogSxVpalEql0cRSHl6V9j569Ejd74sihIBCoYBCoXjRv3VgZ2eHKlWqlCREg3pV3mddVbb2AqXX5sp0zIiIiIiIiKhiMvjEbUREBHr06IFOnToVOXEbFRWFyMhIrfSkpCRkZmYWO4b09HS4yKvAMvcZJIrHxS6nMJa5z+BVwwMKhQKJiYllUoc+lEolUlNTIYTQeEL5q+pVaG9qaiq+XL4Cz54/120HiQRV5VXwMOnRix8ldGBpYYFJE96Gvb19CSI1nFfhfdZHZWsvUHptVigUpRgVERERERERUekz6MTt+vXrcebMGZw8eVKn/DNmzMDUqVPVr9PS0uDh4QG5XA47O7tix6FQKJCY9AjVTS1hb+tU7HIK8+xxGuJv3YatrS1cXFzKpA59KJVKSCQSyOXySjHh8yq0Nz09HZevXUe7YePg7Ope9A5CwDL3GaqbWgI6XHGb/OAODv24CqampkbRR4vjVXif9VHZ2guUXptlMlkpRkVERERERERU+gw2cXv79m1MnjwZcXFxOp9AS6VSSKVSrXQTE5MSncCrljCARAJIymjy4//rkEgkRjPBoorFWOIpaxW9vap+6lzNA66eXkXvIJSQKB6/+DFCl35thH20OCr6+6yvytZeoHTaXJmOFxEREREREVVMBpu4PX36NBITE9G0aVN1Wm5uLg4fPozly5cjKysLpqamhgqPiIiIiIiIiIiIyGAMNnHbsWNHXLhwQSNt5MiR8PX1xXvvvcdJWyIiIiIiIiIiIqq0DDZxa2triwYNGmikWVtbw9nZWSudiIiIiIiIiIiIqDLhIn9ERERERERERERERsZgV9zm5+DBg4YOgYiIiIiIiIiIiMjgeMUtERERERERERERkZHhxC0RERERERERERGRkeHELREREREREREREZGR4cQtERERERERERERkZHhxC0RERERERERERGRkeHELREREREREREREZGR4cQtERERERERERERkZHhxC0RERERERERERGRkeHELREREREREREREZGR4cQtERERERERERERkZHhxC0RERERERERERGRkeHELREREREREREREZGR4cQtERERERERERERkZHhxC0RERERERERERGRkeHELREREREREREREZGR4cQtERERERERERERkZHhxC0RERERERERERGRkeHELREREREREREREZGR4cQtERERERERERERkZHhxC0RERERERERERGRkeHELREREREREREREZGR4cQtERERERERERERkZHhxC0RERERERERERGRkeHELREREREREREREZGRMTN0AERERERERFQ53bp1Czdv3sTTp08hl8vh5+cHqVRq6LCIiIiMAiduiYiIiIiIqNwkJCQgOjoa69evx507dyCEUG+zsLBAUFAQxo4di759+8LEhDeJEhFR5cVRkIiIiIiIiMrFpEmT0KhRI8THx2Pu3Lm4fPkyUlNT8fz5czx48AC//fYb2rRpg48//hgNGzbEyZMnDR0yERGRwfCKWyIiIiIiIioX1tbWuHHjBpydnbW2ubi4oEOHDujQoQNmzZqFXbt24fbt22jevLkBIiUiIjI8TtwSERERERFRuYiKitI5b9euXcswEiIiIuPHiVsiIiIiIiIymMTERFy5cgUAULduXbi4uBg4IiIiIuPANW6JiIiIiIio3CkUCgwfPhxubm5o164d2rVrBzc3NwwbNgypqal6lRUVFYXmzZvD1tYWLi4uCA0NVU8GF2bjxo3w9fWFTCaDv78/fvvtt+I2h4iIqNRx4paIiIiIiIjK3ejRo/HHH39gx44dSElJQUpKCnbs2IFTp05h3LhxepV16NAhRERE4MSJE4iLi0N2dja6dOmCjIyMAvc5duwYBg8ejDfffBNnz55FaGgoQkNDcfHixZI2jYiIqFRwqQQiIiIiIiIqdzt27MDu3bvRpk0bdVpISAi+/vprvde33bVrl8brtWvXwsXFBadPn0bbtm3z3Wfp0qXo2rUrpk+fDgD45JNPEBcXh+XLl2PlypVa+bOyspCVlaV+nZaWBgBQKpVQKpV6xfsyIQQkEglU/yPjIoEEEokEQogSvc9FUfUDIZFAKWE/MDZCUr79QCIRkEjKrh4qnhfvS8n7gT77cuKWiIiIiIiIyp2zszPs7e210u3t7eHo6FiislVLLTg5ORWY5/jx45g6dapGWkhICLZt25Zv/qioKERGRmqlJyUlITMzs9ixKhQK+Hj5wNrSGjJTWbHLobKRaZmJDK8MKBQKJCYmllk9CoUCHj4+UFhbI1HGfmBsFJmZ8Mgon37g4+MBa2sFZLKyq4eKJzNTgYwMjxL3A4VCoXNeTtwSERERERFRufvoo48wdepUrFu3Dq6urgCABw8eYPr06Zg5c2axy1UqlZgyZQoCAwPRoEGDAvM9ePAAVatW1UirWrUqHjx4kG/+GTNmaEz0pqWlwcPDA3K5HHZ2dsWONz09HVfjr8KhkQOs7ayLXQ6VjYxnGUiJT1Gvn1xW0tPTcfvqVdg6OMDFmv3A2KRnZOB2Svn0g6tXb8PBwRbW1nxQo7HJyEhHSsrtEvcDmR4/znDiloiIiIiIiMpddHQ0rl27hho1aqBGjRoAgFu3bkEqlSIpKQmrVq1S5z1z5ozO5UZERODixYs4cuRIqcYrlUohlUq10k1MTGBiUvzHx6huu1X9j4yLgFDfvl6S97koqn4gEQImgv3A2EhE+fYDISQQgo+lMjYv3peS9wN99uXELREREREREZW70NDQUi9zwoQJ2LFjBw4fPgx3d/dC87q6uuLhw4caaQ8fPlRf/UtERGRonLglIiIiIiKicjdr1qxSK0sIgYkTJ2Lr1q04ePAgvLy8itynVatW2LdvH6ZMmaJOi4uLQ6tWrUotLiIiopLgdddERERGICoqCs2bN1evlxQaGoorV64Uud/GjRvh6+sLmUwGf39//Pbbb+UQLRERUek5deoU1q1bh3Xr1uH06dPFKiMiIgI//vgjYmNjYWtriwcPHuDBgwd49uyZOs+IESMwY8YM9evJkydj165dWLhwIf755x/Mnj0bp06dwoQJE0rcJiIiotLAiVsiIiIjcOjQIURERODEiROIi4tDdnY2unTpgoyMjAL3OXbsGAYPHow333wTZ8+eRWhoKEJDQ3Hx4sVyjJyIiKh47ty5g6CgILz22muYPHkyJk+ejObNm6NNmza4c+eOXmVFR0cjNTUVwcHBqFatmvpvw4YN6jy3bt3C/fv31a9bt26N2NhYrF69Go0aNcKmTZuwbdu2Qh9oRkREVJ64VAIREZER2LVrl8brtWvXwsXFBadPn0bbtm3z3Wfp0qXo2rUrpk+fDgD45JNPEBcXh+XLl2PlypVa+bOyspCVlaV+nZaWBuDF07eVSmWxYxdCQKlUIiMzE4pil1K4jMxMPM/ORkJCAoSOD+yws7NDlSpVyiQepVKpbndl8Sq0+dGjR+p+XxQhBO7du4eLFy9CIpHotI+NjQ2cnZ1LEmKhyrJPA6/Ge6yPytZeoPTaXFrHbPTo0cjOzsbff/+NunXrAgCuXLmCkSNHYvTo0VpjY2F0GRsOHjyolda/f3/0799f53qIiIjKEyduiYiIjFBqaioAwMnJqcA8x48fx9SpUzXSQkJCsG3btnzzR0VFITIyUis9KSkJmZmZxY713r17MLO2xhULMySJnGKXU5jHFmaQOdhjxXcrYG5hrtM+tpa2mDZ5Guzt7Us9HqVSidTUVAghyvTJwsakorc5NTUV0V98gWyFbj8vKJVKmFlb4/aNaxBK3X4syDIzhY27HGZmZfMVuyz7NFDx32N9Vbb2AqXXZoWO/46KcujQIRw7dkw9aQsAdevWxbJlyxAUFFQqdRAREVVknLglIiIyMkqlElOmTEFgYGCht2s+ePAAVatW1UirWrUqHjx4kG/+GTNmaEz0pqWlwcPDA3K5HHZ2dsWO9+LFi7h9/RrEvUuwsiybrxZPnufi6rMcOLRTwrKKZZH5nyU/Q9bvWTA1NYWLi0upx6NUKiGRSCCXyyvVhE9FbnN6ejpunDmD/0il8LAsug9lZGbiioUZxN1LMDEt+orbxGwlvspRwiHQT6c+qq+y7tNAxX+P9VXZ2guUXptlMlmpxOPh4YHs7Gyt9NzcXFSvXr1U6iAiIqrIOHFLRERkZCIiInDx4kUcOXKkVMuVSqWQSqVa6SYmJiU6gZdIJC+u3jKVwMxct1vK9WWqBGAKyKrIYFXVqsj8AgKZIhMSiaTMJmRUZVeWCR+gYrdZ1U9ryGSobVV0H1IASBI5sLI006lfm2blAE+VOvdRfZVHnwYq9ntcHJWtvUDptLm0jteCBQswceJEfPXVV2jWrBmAFw8qmzx5Mr744otSqYOIiKgi48QtERGREZkwYQJ27NiBw4cPw93dvdC8rq6uePjwoUbaw4cP4erqWpYhEhERFZujo6PGutEZGRlo0aKFeomRnJwcmJmZYdSoUQgNDTVQlERERMaBE7dERERGQAiBiRMnYuvWrTh48CC8vLyK3KdVq1bYt28fpkyZok6Li4tDq1atyjBSIiKi4luyZImhQyAiIqowOHFLRERkBCIiIhAbG4vt27fD1tZWvU6tvb09LP9/Pc4RI0bAzc0NUVFRAIDJkyejXbt2WLhwIXr06IH169fj1KlTWL16tcHaQUREVJiwsDBDh0BERFRhVJ7FnIiIiIxYdHQ0UlNTERwcjGrVqqn/NmzYoM5z69Yt3L9/X/26devWiI2NxerVq9GoUSNs2rQJ27ZtK/SBZkRERIaUkZFRpvmJiIheJbziloiIyAgIIYrMc/DgQa20/v37o3///mUQERERUenz9vbG5MmTERYWhmrVquWbRwiBvXv3YtGiRWjbti1mzJhRzlESEREZB07cEhERERERUbk4ePAgPvjgA8yePRuNGjVCs2bNUL16dchkMjx58gSXL1/G8ePHYWZmhhkzZmDcuHGGDpmIiMhgOHFLRERERERE5aJu3brYvHkzbt26hY0bN+L333/HsWPH8OzZM1SpUgVNmjTB119/jW7dusHU1NTQ4RIRERkUJ26JiIiIiIioXNWoUQPvvPMO3nnnHUOHQkREZLT4cDIiIiIiIiIiIiIiI8OJWyIiIiIiIiIiIiIjw4lbIiIiIiIiIiIiIiPDiVsiIiIiIiIiIiIiI8OJWyIiIiIiIiIiIiIjY2boAIiIiIiIiKhySklJwZ9//onExEQolUqNbSNGjDBQVERERMaBE7dERERERERU7n755RcMHToU6enpsLOzg0QiUW+TSCScuCUiokqPSyUQERERERFRuXvnnXcwatQopKenIyUlBU+ePFH/PX782NDhERERGRwnbomIiIiIiKjc3b17F5MmTYKVlZWhQyEiIjJKnLglIiIiIiKichcSEoJTp04ZOgwiIiKjZdA1bqOjoxEdHY2EhAQAgJ+fHz7++GN069bNkGERERERERFRGfj555/V/92jRw9Mnz4dly9fhr+/P8zNzTXy9u7du7zDIyIiMioGnbh1d3fHZ599Bh8fHwgh8P333+P111/H2bNn4efnZ8jQiIiIiIiIqJSFhoZqpc2ZM0crTSKRIDc3txwiIiIiMl4Gnbjt1auXxut58+YhOjoaJ06c4MQtERERERHRK0apVBo6BCIiogrDoBO3L8vNzcXGjRuRkZGBVq1a5ZsnKysLWVlZ6tdpaWkAXgz+JfkCIISARCIBhABEGX2R+P86hBBG8WVFqVQaTSzl4VVor979VIj//UG3/MbUR4vjVXif9VHZ2guUXpsr0zEjIiIyVj/88AMGDhwIqVSqkf78+XOsX78eI0aMMFBkRERExsHgE7cXLlxAq1atkJmZCRsbG2zduhX169fPN29UVBQiIyO10pOSkpCZmVnsGNLT0+EirwLL3GeQKB4Xu5zCWOY+g1cNDygUCiQmJpZJHfpQKpVITU2FEAImJq/+M+pehfYqFAp41fDQo58KSJ4pAAnw//+nUMbWR4vjVXif9VHZ2guUXpsVCkUpRkVERETFMXLkSHTt2hUuLi4a6QqFAiNHjuTELRERVXoGn7itW7cuzp07h9TUVGzatAlhYWE4dOhQvpO3M2bMwNSpU9Wv09LS4OHhAblcDjs7u2LHoFAokJj0CNVNLWFv61Tscgrz7HEa4m/dhq2trdYXE0NQKpWQSCSQy+WVYsLnVWhveno64m/dRhNd+6kQgACEjRMgKXri1tj6aHG8Cu+zPipbe4HSa7NMJivFqIiIiKg41HeU5XHnzh3Y29sbICIiIiLjYvCJWwsLC3h7ewMAAgICcPLkSSxduhSrVq3SyiuVSrVuowEAExOTEp3Aq24Ph0QCSMpo8uP/65BIJEYzwaKKxVjiKWsVvb3691Pl/+fVMb8R9tHiqOjvs74qW3uB0mlzZTpeRERExqZJkyaQSCSQSCTo2LEjzMz+d1qam5uL+Ph4dO3a1YAREhERGQeDT9zmpVQqNdaxJSIiIiIioldHaGgoAODcuXMICQmBjY2NepuFhQVq1qyJvn37Gig6IiIi42HQidsZM2agW7duqFGjBhQKBWJjY3Hw4EHs3r3bkGERERERERFRGZk1axYAoGbNmhg4cCCXMCIiIiqAQSduExMTMWLECNy/fx/29vZo2LAhdu/ejc6dOxsyLCIiIiIiIipjYWFhhg6BiIjIqBl04vbbb781ZPVERERERERkII6Ojvk+nEwikUAmk8Hb2xvh4eEYOXKkAaIjIiIyPKNb45aIiIiIiIhefR9//DHmzZuHbt264bXXXgMA/Pnnn9i1axciIiIQHx+Pt956Czk5ORgzZoyBoyUiIip/nLglIiIiIiKicnfkyBHMnTsX48eP10hftWoV9uzZg82bN6Nhw4b48ssvOXFLRESVkomhAyAiIiIiIqLKZ/fu3ejUqZNWeseOHdUPrO7evTtu3LhR3qEREREZhWJN3NaqVQvJycla6SkpKahVq1aJgyIiIqooOCYSEREVj5OTE3755Ret9F9++QVOTk4AgIyMDNja2pZ3aEREREahWEslJCQkIDc3Vys9KysLd+/eLXFQREREFQXHRCIiouKZOXMm3nrrLRw4cEC9xu3Jkyfx22+/YeXKlQCAuLg4tGvXzpBhEhERGYxeE7c///yz+r93794Ne3t79evc3Fzs27cPNWvWLLXgiIiIjBXHRCIiopIZM2YM6tevj+XLl2PLli0AgLp16+LQoUNo3bo1AOCdd94xZIhEREQGpdfEbWhoKABAIpEgLCxMY5u5uTlq1qyJhQsXllpwRERExopjIhERUckFBgYiMDDQ0GEQEREZJb0mbpVKJQDAy8sLJ0+eRJUqVcokKCIiImPHMZGIiKjklEolrl27hsTERPXYqtK2bVsDRUVERGQcirXGbXx8fGnHQUREVCFxTCQiIiqeEydOYMiQIbh58yaEEBrbJBJJvmvIExERVSbFmrgFgH379mHfvn35/jL63XfflTgwIiKiioJjIhERkf7Gjx+PZs2a4ddff0W1atUgkUgMHRIREZFRKdbEbWRkJObMmYNmzZpxgCUiokqNYyIREVHxXL16FZs2bYK3t7ehQyEiIjJKxZq4XblyJdauXYvhw4eXdjxEREQVCsdEIiKi4mnRogWuXbvGiVsiIqICFGvi9vnz52jdunVpx0JERFThcEwkIiIqnokTJ+Kdd97BgwcP4O/vD3Nzc43tDRs2NFBkRERExqFYE7ejR49GbGwsZs6cWdrxEBERVSgcE4mIiIqnb9++AIBRo0ap0yQSCYQQfDgZERERijlxm5mZidWrV2Pv3r1o2LCh1i+jixYtKpXgiIiIjB3HRCIiouKJj483dAhERERGrVgTt3/99RcaN24MALh48aLGNj6UhYiIKhOOiURERMXj6elp6BCIiIiMWrEmbg8cOFDacRAREVVIHBOJiIiKb926dVi5ciXi4+Nx/PhxeHp6YsmSJfDy8sLrr79u6PCIiIgMysTQARAREREREVHlEx0djalTp6J79+5ISUlRr2nr4OCAJUuWGDY4IiIiI1CsK27bt29f6O2f+/fvL3ZAREREFQnHRCIiouJZtmwZvv76a4SGhuKzzz5Tpzdr1gzTpk0zYGRERETGoVgTt6q1/FSys7Nx7tw5XLx4EWFhYaURFxERUYXAMZGIiKh44uPj0aRJE610qVSKjIwMA0RERERkXIo1cbt48eJ802fPno309PQSBURERFSRcEwkIiIqHi8vL5w7d07rIWW7du1CvXr19Crr8OHDWLBgAU6fPo379+9j69atCA0NLTD/wYMH0b59e630+/fvw9XVVa+6iYiIykqprnE7bNgwfPfdd6VZJBERUYXEMZGIiKhwU6dORUREBDZs2AAhBP7880/MmzcPM2bMwLvvvqtXWRkZGWjUqBG++uorvfa7cuUK7t+/r/5zcXHRa38iIqKyVKwrbgty/PhxyGSy0iySiIioQuKYSEREVLjRo0fD0tISH330EZ4+fYohQ4agevXqWLp0KQYNGqRXWd26dUO3bt30jsHFxQUODg465c3KykJWVpb6dVpaGgBAqVRCqVTqXbeKEAISiQSq/5FxkUACiUQCIUSJ3ueiqPqBkEigLOT5CWQYQlK+/UAiEZBIyq4eKp4X70vJ+4E++xZr4rZPnz4ar4UQuH//Pk6dOoWZM2cWp0giIqIKiWMiERFR8Q0dOhRDhw7F06dPkZ6eDhcXFzx9+hTHjh1D69aty7z+xo0bIysrCw0aNMDs2bMRGBhYYN6oqChERkZqpSclJSEzM7PYMSgUCvh4+cDa0hoyU/7oa2wyLTOR4ZUBhUKBxMTEMqtHoVDAw8cHCmtrJPLHf6OjyMyER0b59AMfHw9YWysgk5VdPVQ8mZkKZGR4lLgfKBQKnfMWa+LW3t5e47WJiQnq1q2LOXPmoEuXLsUpkoiIqELimEhERFRyVlZWsLKyAgBcvXoVQUFByM3NLbP6qlWrhpUrV6JZs2bIysrCN998g+DgYPzxxx9o2rRpvvvMmDEDU6dOVb9OS0uDh4cH5HI57Ozsih1Leno6rsZfhUMjB1jbWRe7HCobGc8ykBKfAltb2zJdSiM9PR23r16FrYMDXKzZD4xNekYGbqeUTz+4evU2HBxsYW3NpVuMTUZGOlJSbpe4H+hzZ2axJm7XrFlTnN2IiIheORwTiYiIKp66deuibt266tetW7fG9evXsXjxYqxbty7ffaRSKaRSqVa6iYkJTEyK//gY1W23qv+RcREQ6tvXS/I+F0XVDyRCwESwHxgbiSjffiCEBEKUXT1UPC/el5L3A332LVEvOH36NH788Uf8+OOPOHv2bEmKIiIiqtBKOiYePnwYvXr1QvXq1SGRSLBt27ZC8x88ePD/17/S/Hvw4EExW0BERP/X3n3HR1Xl/x9/3ySQnkAgIZRQRUCUvmBYpSgSxC+KDd1lBUVYQUEEa74WRERUqgqKihRXUHRFfq71i4BLEaSjIIRexEBoSSYhCSFzfn9gRsZQMkkmc5O8nj7GB7fMuZ8z9wxn5sOZc1CxtW/fXrt27fJ1GAAAuBRpxG1KSoruvvtuff/9966J3FNTU9W1a1d99NFHio6OLskYAQCwrZLqE/NXwx4wYECBeXMvJikpye3nmayGDQBA0WzatEk1a9b0dRgAALgUKXE7bNgwORwObd26Vc2aNZMk/fLLL+rfv78efvhhffjhhyUaJAAAdlVSfWJ5WA1bfn4ylpd+0vX7z1ALu+K2t1eAdjqdXl9V2G7Kep09Xa3baVkysmT8/GQKs7q3h23UU6WxqnlZv8eeqmj1lUquzsV9/ueff37R43v37vW4zIyMDLfRsnv37tWmTZsUFRWlunXrKjExUYcOHdL7778vSZoyZYoaNGig5s2bKzs7WzNmzNCSJUv0f//3fx5fGwAAbylS4vabb77Rd9995/qCKklXXHGFpk2bxkIsAIAKxdd9oh1Ww87Ly1OdBg1lRVVSTpB/kcu5qNw8XZ6Tp6rB9Qu14ra3V4B2Op1KS0uTMcar85zZSVmvs6erdWfl5CjP5Ol01QDlBRQiEethG/VUaaxqXtbvsacqWn2lkquzJ6thn0/v3r0veY5VmH8wOce6devUtWtX13b+ImL9+/fX7NmzlZycrAMHDriOnz59Wo8++qgOHTqkkJAQtWjRQt99951bGQAA+FqRErdOp1OVKlUqsL9SpUoV6l+sAQDwVZ9op9Ww/f399evePTJHtyswpEgfLS7t9BntOHVGNboFFGrFbW+vAO10OmVZlqKjoytUwqcs19nT1bodp07J35xR5WPbFVCYxK2HbdRTpbGqeVm/x56qaPWVSq7OnqyGfaE4SlqXLl1kLrKg0+zZs922n3jiCT3xxBMlHgcAACWpSN+urrvuOg0fPlwffvihatWqJUk6dOiQRowYoeuvv75EAwQAwM581SfacTVsOZ2yjJeS1b9P51DYFbdLYwXo/LIrSsJHKtt19nS1br/fz7WcTlmmEIlbD9uop0pzVfOyeo+LoqLVVyqZOlek1wsAAF8qUo87depUpaenq379+mrUqJEaNWqkBg0aKD09XW+88UZJxwgAgG3ZqU9kNWwAAAAAKD+KNOI2Li5OGzZs0Hfffaft27dLkpo1a6Zu3bqVaHAAANidnfpEVsMGAAAAgPLDo8TtkiVLNHToUK1evVoRERG64YYbdMMNN0iS0tLS1Lx5c02fPl3XXnutV4IFAMAuSrpPZDVsAAAAAMC5PJoqYcqUKRo0aNB5FzCJjIzUAw88oEmTJpVYcAAA2FVJ94nr1q1T69at1bp1a0lnV8Nu3bq1nnvuOUm64GrYV111lTp37qzNmzfru+++Y655AAAAACgnPErcbt68WT169Ljg8e7du2v9+vXFDgoAALsr6T4xfzXsPz/yV8GePXu2vv/+e9f5TzzxhHbt2qWsrCwdP35cS5cuVdeuXYtaHQAAfCI1NVUzZsxQYmKiTpw4IUnasGGDDh065OPIAADwPY+mSjhy5IgqVap04cICAnT06NFiBwUAgN3RJwIAUDw//fSTunXrpsjISO3bt0+DBg1SVFSUFixYoAMHDrimBwIAoKLyaMRt7dq1tWXLlgse/+mnn1gUBQBQIdAnAgBQPCNHjtS9996rnTt3KigoyLW/Z8+eWrZsmQ8jAwDAHjxK3Pbs2VPPPvussrOzCxzLysrSqFGj9D//8z8lFhwAAHZFnwgAQPGsXbtWDzzwQIH9tWvX1uHDh30QEQAA9uLRVAnPPPOMFixYoMsvv1xDhw5VkyZNJEnbt2/XtGnTlJeXp6efftorgQIAYCf0iQAAFE9gYKDS09ML7N+xY4eio6N9EBEAAPbiUeK2Ro0a+uGHHzRkyBAlJibKGCNJsixLCQkJmjZtmmrUqOGVQAEAsBP6RAAAiufmm2/WCy+8oI8//ljS2T70wIEDevLJJ3X77bf7ODoAAHzPo8StJNWrV09fffWVTp48qV27dskYo8aNG6tq1areiA8AANuiTwQAoOgmTpyoO+64QzExMcrKylLnzp11+PBhxcfHa+zYsb4ODwAAn/M4cZuvatWq+stf/lKSsQAAUCbRJwIA4LnIyEgtWrRIK1as0E8//aSMjAy1adNG3bp183VoAADYQpETtwAAAAAAFNc111yja665xtdhAABgOyRuAQAAAACl7vXXXz/vfsuyFBQUpMsuu0ydOnWSv79/KUcGAIA9kLgFAAAAAJS6yZMn6+jRozp16pRrfviTJ08qJCREYWFhSklJUcOGDbV06VLFxcX5OFoAAEqfn68DAAAAAABUPC+99JL+8pe/aOfOnTp+/LiOHz+uHTt2qEOHDnrttdd04MABxcbGasSIEb4OFQAAn2DELQAAAACg1D3zzDP69NNP1ahRI9e+yy67TBMmTNDtt9+uPXv26NVXX9Xtt9/uwygBAPAdRtwCAAAAAEpdcnKyzpw5U2D/mTNndPjwYUlSrVq15HA4Sjs0AABsgcQtAAAAAKDUde3aVQ888IA2btzo2rdx40YNGTJE1113nSTp559/VoMGDXwVIgAAPkXiFgAAAABQ6t577z1FRUWpbdu2CgwMVGBgoNq1a6eoqCi99957kqSwsDBNnDjRx5ECAOAbzHELAAAAACh1sbGxWrRokbZv364dO3ZIkpo0aaImTZq4zunatauvwgMAwOdI3AIAAAAAfKZp06Zq2rSpr8MAAMB2SNwCAAAAAHzi119/1eeff64DBw7o9OnTbscmTZrko6gAALAHErcAAAAAgFK3ePFi3XzzzWrYsKG2b9+uK6+8Uvv27ZMxRm3atPF1eAAA+ByLkwEAAAAASl1iYqIee+wx/fzzzwoKCtKnn36qgwcPqnPnzrrzzjt9HR4AAD5H4hYAAAAAUOq2bdumfv36SZICAgKUlZWlsLAwvfDCC3rllVd8HB0AAL5H4hYAAAAAUOpCQ0Nd89rWrFlTu3fvdh07duyYr8ICAMA2mOMWAAAAAFDqrr76aq1YsULNmjVTz5499eijj+rnn3/WggULdPXVV/s6PAAAfI7ELQAAAACg1E2aNEkZGRmSpNGjRysjI0Pz589X48aNNWnSJB9HBwCA75G4BQAAAACUqry8PP36669q0aKFpLPTJkyfPt3HUQEAYC8+neN23Lhx+stf/qLw8HDFxMSod+/eSkpK8mVIAAAAAAAv8/f3V/fu3XXy5ElfhwIAgG35NHH73//+Vw899JBWr16tRYsWKTc3V927d1dmZqYvwwIAAAAAeNmVV16pPXv2+DoMAABsy6dTJXzzzTdu27Nnz1ZMTIzWr1+vTp06+SgqAAAAAIC3vfjii3rsscc0ZswYtW3bVqGhoW7HIyIifBQZAAD2YKs5btPS0iRJUVFR5z2ek5OjnJwc13Z6erokyel0yul0Fvm6xhhZliUZI5mil3OJi8iyLBljihVrSXE6nbaJpTSUh/p63E6N+eOhwp1vpzZaFE6nU6mpqXI4HGdfKy+IiIhQ9erVvVK2p8pDu/ZUSdW5Ir1mAADYVc+ePSVJN998s9tnt/zPvXl5eb4KDQAAW7BN4tbpdOqRRx7RX//6V1155ZXnPWfcuHEaPXp0gf1Hjx5VdnZ2ka+dkZGhmOjqCs7LkuU4UeRyLiY4L0sN6sbJ4XAoJSXFK9fwhNPpVFpamowx8vPz6YwZpaI81NfhcKhB3TgP2qmRleWQLOn3/12U3dpoUaSmpuqj+R/rQPLh3xPWJS+4cmU9PPRBRUZGeqV8T5SHdu2pkqqzw+EowagAAEBRLF261NchAABga7ZJ3D700EPasmWLVqxYccFzEhMTNXLkSNd2enq64uLiFB0dXayf0TgcDqUcPaZa/sGKDD//aN/iyjqRrr0HDroWYvM1p9Mpy7IUHR1dIRI+5aG+GRkZ2nvgoFoXtp0aIxnJhEVJhRh9arc2WhQOh0MHkw+r7rU9VC02rsTLP374V/33g7fl7+9vi9eoPLRrT5VUnYOCgkowKgAAUBSdO3f2dQgAANiaLRK3Q4cO1RdffKFly5apTp06FzwvMDBQgYGBBfb7+fkV6wt8/s/DZVmS5aXkx+/XsCzLNgmW/FjsEo+3lfX6et5Onb+fW8jzbdhGPZX/GlWLjVNs/YbeuIDtXqOy3q6LoiTqXJFeLwAA7Gz58uV6++23tWfPHn3yySeqXbu2/vWvf6lBgwa65pprfB0eAAA+5dNvrsYYDR06VJ999pmWLFmiBg0a+DIcAAAAAEAp+fTTT5WQkKDg4GBt2LDBtZ5JWlqaXnrpJR9HBwCA7/k0cfvQQw/pgw8+0Lx58xQeHq7Dhw/r8OHDysrK8mVYAAAAAAAve/HFFzV9+nS9++67qlSpkmv/X//6V23YsMGHkQEAYA8+Tdy+9dZbSktLU5cuXVSzZk3XY/78+b4MCwAAAADgZUlJSerUqVOB/ZGRkUpNTS39gAAAsBmfznFrvLTqOwAAAADA3mJjY7Vr1y7Vr1/fbf+KFSvUsKEX1isAAKCMYXUWAAAAAECpGzRokIYPH64ff/xRlmXpt99+09y5c/XYY49pyJAhvg4PAACf8+mIWwAAAABAxfTUU0/J6XTq+uuv16lTp9SpUycFBgbqscce07Bhw3wdHgAAPkfiFgAAAABQ6izL0tNPP63HH39cu3btUkZGhq644gqFhYX5OjQAAGyBqRIAAAAAAKXugw8+0KlTp1S5cmVdccUVat++PUlbAADOQeIWAAAAAFDqRowYoZiYGP3973/XV199pby8PF+HBACArZC4BQAAAACUuuTkZH300UeyLEt9+vRRzZo19dBDD+mHH37wdWgAANgCiVsAAAAAQKkLCAjQ//zP/2ju3LlKSUnR5MmTtW/fPnXt2lWNGjXydXgAAPgci5MBAAAAAHwqJCRECQkJOnnypPbv369t27b5OiQAAHyOEbcAAAAAAJ84deqU5s6dq549e6p27dqaMmWKbr31Vm3dutXXoQEA4HMkbgEAAAAApe7uu+9WTEyMRowYoYYNG+r777/Xrl27NGbMGDVt2tSjspYtW6ZevXqpVq1asixLCxcuvORzvv/+e7Vp00aBgYG67LLLNHv27KJVBAAALyFxCwAAAAAodf7+/vr444+VnJysqVOnKj4+3nVsy5YtHpWVmZmpli1batq0aYU6f+/evbrpppvUtWtXbdq0SY888ogGDhyob7/91qPrAgDgTcxxCwAAAAAodXPnznXbdjgc+vDDDzVjxgytX79eeXl5hS7rxhtv1I033ljo86dPn64GDRpo4sSJkqRmzZppxYoVmjx5shISEgpdDgAA3kTiFgAAAADgM8uWLdN7772nTz/9VLVq1dJtt91W6JGzRbVq1Sp169bNbV9CQoIeeeSRCz4nJydHOTk5ru309HRJktPplNPpLHIsxhhZlqX8/2AvlixZliVjTLHu86XktwNjWXJatAO7MVbptgPLMrIs710HRXP2vhS/HXjyXBK3AADYwLJlyzR+/HitX79eycnJ+uyzz9S7d++LPuf777/XyJEjtXXrVsXFxemZZ57RvffeWyrxAgBQHIcPH9bs2bP13nvvKT09XX369FFOTo4WLlyoK664olSuX6NGDbd9NWrUUHp6urKyshQcHFzgOePGjdPo0aML7D969Kiys7OLHIvD4VDjBo0VGhyqIP+gIpcD78gOzlZmg0w5HA6lpKR47ToOh0NxjRvLERqqlCDagd04srMVl1k67aBx4ziFhjoUFOS966BosrMdysyMK3Y7cDgchT6XxC0AADaQPzffgAEDdNttt13y/Py5+QYPHqy5c+dq8eLFGjhwoGrWrMlPPAEAttarVy8tW7ZMN910k6ZMmaIePXrI399f06dP93VoF5WYmKiRI0e6ttPT0xUXF6fo6GhFREQUudyMjAzt3LtTVVpWUWhEaEmEihKUmZWp1L2pCg8PV0xMjNeuk5GRoYM7dyq8ShXFhNIO7CYjM1MHU0unHezceVBVqoQrNNR710HRZGZmKDX1YLHbQZAH/zhD4hYAABtgbj4AQEXx9ddf6+GHH9aQIUPUuHFjn8QQGxurI0eOuO07cuSIIiIizjvaVpICAwMVGBhYYL+fn5/8/Iq+7nf+z27z/4O9GBnXz9eLc58vJb8dWMbIz9AO7MYypdsOjLFkjPeug6I5e1+K3w48eS6JWwAAyiA7zs0nPz8Zy0sfMH//UlzY+f+8PR+d0+n0+hxn3nbs2DFXGygMY4wcDoccDsfZ++0FERERql69ulfK9nTuQKdlyciS8fOTKUx9PWyjnrJkKedUjlatWqV9+/aVePnS2deoUqVKHt1jb94zbyuN97Gn7zNvM8YoLy+v2PesuK/ZihUr9N5776lt27Zq1qyZ7rnnHt19993FKtNT8fHx+uqrr9z2LVq0SPHx8aUaBwAAF0PiFgCAMshOc/Pl5eWpToOGsqIqKSfIv8jlXFRuni7PyVPV4PqFmv/P2/PROZ1OpaWlyRjj1VEX3pKWlqYJr02QI6vw82tZlqWaNWoq+UiyjJdGAoUHh+ux4Y8pMjKyxMv2dO7ArJwc5Zk8na4aoLyAQiQxPWyjnkrNSZUzIFMfv/2W1xYvsvws1Wh0mU7mZsovoHDt2pv3zNu8/T4uyvvM2yzLUsO4hrrnb/eoSpUqRS7Hk7n5zufqq6/W1VdfrSlTpmj+/PmaOXOmRo4cKafTqUWLFikuLk7h4eEelZmRkaFdu3a5tvfu3atNmzYpKipKdevWVWJiog4dOqT3339fkjR48GBNnTpVTzzxhAYMGKAlS5bo448/1pdfflmsugEAUJJI3AIAUEF4a24+f39//bp3j8zR7QoM8dJHi9NntOPUGdXoFlCo+f+8PR+d0+mUZVmKjo4uk4nbjIwMbfhlgwKvDVRwtfP/JPjPLFkKDQ7V0RpHvfJT3qzjWcpZniN/f3+v3DNP5w50nDolf3NGlY9tV0BhErcetlFPHTp4SMc2bNaI6GDVDq1c4uVLUkqetDjET0fanlFQ9UIkt718z7zN2+/jorzPvC37eLZ0UMW+Z57MzXcxoaGhGjBggAYMGKCkpCS99957evnll/XUU0/phhtu0Oeff17ostatW6euXbu6tvP7u/79+2v27NlKTk7WgQMHXMcbNGigL7/8UiNGjNBrr72mOnXqaMaMGUw3BACwFRK3AACUQXacm09OpyzjpZ8c/z6dQ2Hn/yuN+ejyyy6Lidv8exZULUghNUIK9xxZCvQPVEhEiFcSt0ZG2Sbba/fM07kD/X4/13I6ZZlCJG49bKOecpqz5desbKleqHc+wvvnOiV/Kah64dqFt+9ZafDm+7go77PSYH4t3bn5CqtJkyZ69dVXNW7cOP3nP//RzJkzPXp+ly5dLvprgNmzZ5/3ORs3bvQ0VAAASk3Z/IQFAEAFFx8fr8WLF7vtY24+AEBZ5+/vr969e3s02hYAgPKKxC0AADaQkZGhTZs2adOmTZL+mJsv/2ediYmJ6tevn+v8wYMHa8+ePXriiSe0fft2vfnmm/r44481YsQIX4QPAAAAAChhJG4BALCBdevWqXXr1mrdurWks3PztW7dWs8995wkXXBuvkWLFqlly5aaOHEic/MBAAAAQDnCHLcAANgAc/MBAAAAAM7FiFsAAAAAAAAAsBkStwAAAAAAAABgMyRuAQAAAAAAAMBmSNwCAAAAAAAAgM2QuAUAAAAAAAAAmyFxCwAAAAAAAAA2Q+IWAAAAAAAAAGyGxC0AAAAAAAAA2AyJWwAAAAAAAACwGRK3AAAAAAAAAGAzJG4BAAAAAAAAwGZI3AIAAAAAAACAzZC4BQAAAAAAAACbIXELAAAAAAAAADZD4hYAAAAAAAAAbIbELQAAAAAAAADYDIlbAAAAAAAAALAZErcAAAAAAAAAYDMkbgEAAAAAAADAZkjcAgAAAAAAAIDNkLgFAAAAAAAAAJshcQsAAAAAAAAANkPiFgAAAAAAAABshsQtAAAAAAAAANgMiVsAAAAAAAAAsBkStwAAAAAAAABgMyRuAQAAAAAAAMBmSNwCAAAAAAAAgM2QuAUAAAAAAAAAmyFxCwAAAAAAAAA2Q+IWAAAAAAAAAGyGxC0AAAAAAAAA2AyJWwAAAAAAAACwGRK3AAAAAAAAAGAzJG4BAAAAAAAAwGZI3AIAAAAAAACAzZC4BQAAAAAAAACbIXELAAAAAAAAADbj08TtsmXL1KtXL9WqVUuWZWnhwoW+DAcAAAAAAAAAbMGnidvMzEy1bNlS06ZN82UYAAAAAAAAAGArAb68+I033qgbb7zRlyEAAAAAAAAAgO34NHHrqZycHOXk5Li209PTJUlOp1NOp7PI5RpjZFmWZIxkil7OJS4iy7JkjClWrCXF6XTaJpbS4HQ6lZqaKofDcfZee0lERISqV6/ulbI9bqfG/PFQ4c63UxstCq+/l232Gtn1fXzs2DHX388lzRijvLy8Yr/P7PaaAQAAAADwZ2UqcTtu3DiNHj26wP6jR48qOzu7yOVmZGQoJrq6gvOyZDlOFCfECwrOy1KDunFyOBxKSUnxyjU84XQ6lZaWJmOM/PzK/xp1qamp+mj+xzqQfPj3RKZ3BFeurIeHPqjIyMgSL9vhcKhB3TgP2qmRleWQLOn3/12U3dpoUXj7vWy318iO7+O0tDS9PvVNZZ0+7Z0LWJbq1ozV3Xf1UZUqVYpcjMPhKLmYAAAAAADwgjKVuE1MTNTIkSNd2+np6YqLi1N0dLQiIiKKXK7D4VDK0WOq5R+syPCokgi1gKwT6dp74KDCw8MVExPjlWt4wul0yrIsRUdH2ybh400Oh0MHkw+r7rU9VC02zivXOH74V/33g7fl7+/vlXuckZGhvQcOqnVh26kxkpFMWJRUiFHGdmujReHt97LdXiM7vo8zMjL0y67d6vyPB1Qttk6Jl3/88EEdXP5Nsd9nQUFBJRgVAAAAAAAlr0wlbgMDAxUYGFhgv5+fX7GSFvk/fZZlSZaXkh+/X8OyLNskWPJjsUs83pR/j6vFxim2fkNvXcSr99jzdur8/dxCnm/DNuopr7+Xbfga2e197Hqv1YxTbL0GXrnGgRK4B3Z5vQAAAAAAuBC+uQIAAAAAAACAzfh0xG1GRoZ27drl2t67d682bdqkqKgo1a1b14eRAQAAAAAAAIDv+DRxu27dOnXt2tW1nT9/bf/+/TV79mwfRQUAAAAAAAAAvuXTqRK6dOkiY0yBB0lbAAAAAICnpk2bpvr16ysoKEgdOnTQmjVrLnju7NmzZVmW24MFTAEAdsIctwAAAACAMm/+/PkaOXKkRo0apQ0bNqhly5ZKSEhQSkrKBZ8TERGh5ORk12P//v2lGDEAABdH4hYAAJtglBAAAEU3adIkDRo0SPfdd5+uuOIKTZ8+XSEhIZo5c+YFn2NZlmJjY12PGjVqlGLEAABcnE/nuAUAAGfljxKaPn26OnTooClTpighIUFJSUmKiYk573MiIiKUlJTk2rYsq7TCBQDAVk6fPq3169crMTHRtc/Pz0/dunXTqlWrLvi8jIwM1atXT06nU23atNFLL72k5s2bn/fcnJwc5eTkuLbT09MlSU6nU06ns8ixG2PO/iPs7//BXiyd/QdyY0yx7vOl5LcDY1ly8pnOdoxVuu3Asowsy3vXQdGcvS/FbweePJfELQAANnDuKCFJmj59ur788kvNnDlTTz311Hmfkz9KCACAiu7YsWPKy8srMGK2Ro0a2r59+3mf06RJE82cOVMtWrRQWlqaJkyYoI4dO2rr1q2qU6dOgfPHjRun0aNHF9h/9OhRZWdnFzl2h8Ohxg0aKzQ4VEH+/HrGbrKDs5XZIFMOh+Oi024Ul8PhUFzjxnKEhiqFX1HZjiM7W3GZpdMOGjeOU2ioQ0FB3rsOiiY726HMzLhitwOHw1Hoc0ncAgDgY6UxSkjy/kgh+fnJWF6ahcnPT35+foUejeTt0TFOp9PrIy68qSijuyx5dzSYt++ZpyOZnJYlI0vGz0+mMCOfPGyjnvKzzpbv3feZPGoXpTUKzVu8/T624yjKkrpnZfF+n098fLzi4+Nd2x07dlSzZs309ttva8yYMQXOT0xM1MiRI13b6enpiouLU3R0tCIiIoocR0ZGhnbu3akqLasoNCK0yOXAOzKzMpW6N1Xh4eEX/BVUScjIyNDBnTsVXqWKYkJpB3aTkZmpg6ml0w527jyoKlXCFRrqveugaDIzM5SaerDY7cCTKe5I3AIA4GOlMUpI8t5Ioby8PNVp0FBWVCXlBPkXuZyLys3T5Tl5qhpcv1Cjkbw9OsbpdCotLU3GmLPJtDKmqKO7qvtVl5HxSkzevmeejmTKyslRnsnT6aoBygsoRNLNwzbqqeDqwYpp7S+/6GDlhFYu8fIlSWeMasfEqWqwvwL9Ay95emmNQvMWb7+P7TiKMic4RxE1IpSRkVFqI4VKS/Xq1eXv768jR4647T9y5Eihf51SqVIltW7dWrt27Trv8cDAQAUGFnxv+P3+DzdFlZ9Mz/8P9mJkXP8Q480+P78dWMbIz9AO7MYypdsOjLFkTNn7jFnenb0vxW8HnjyXxC0AAGWQp6OEJO+NFPL399eve/fIHN2uwBAvfbQ4fUY7Tp1RjW4BhRqN5O3RMU6nU5ZlKTo6ukwmbosyuit/1OCveb96JbHg7Xvm6Ugmx6lT8jdnVPnYdgUUJnHrYRv11KFjh3Rs42Y548IUGBlc4uVLks44dSjnjNIuD1BIRMglTy+tUWje4u33sR1HUZ7KOqXoI9EKCwsrtZFCpaVy5cpq27atFi9erN69e0s6e48XL16soUOHFqqMvLw8/fzzz+rZs6cXIwUAoPBI3AIA4GOlMUpI8v5IITmdsoyXfj77+3QOhR2NVBqjY/LLLouJ26KO7jLy3ogwb98zT0cy+f1+ruV0yjKFSNx62EY95TS/T2ni5feZJ+2itEaheZM338d2HEVZUvfMrvd75MiR6t+/v9q1a6f27dtrypQpyszMdM0f369fP9WuXVvjxo2TJL3wwgu6+uqrddlllyk1NVXjx4/X/v37NXDgQF9WAwAAF3v2uAAAVCDnjhLKlz9K6NxRtReTP0qoZs2a3goTAABbu+uuuzRhwgQ999xzatWqlTZt2qRvvvnGNRXRgQMHlJyc7Dr/5MmTGjRokJo1a6aePXsqPT1dP/zwg6644gpfVQEAADeMuAUAwAYYJQQAQPENHTr0glMjfP/9927bkydP1uTJk0shKgAAiobELQAANnDXXXfp6NGjeu6553T48GG1atWqwCihc3+amj9K6PDhw6pataratm3LKCEAAAAAKEdI3AIAYBOMEgIAAAAA5GOOWwAAAAAAAACwGRK3AAAAAAAAAGAzJG4BAAAAAAAAwGZI3AIAAAAAAACAzZC4BQAAAAAAAACbIXELAAAAAAAAADZD4hYAAAAAAAAAbIbELQAAAAAAAADYDIlbAAAAAAAAALAZErcAAAAAAAAAYDMkbgEAAAAAAADAZkjcAgAAAAAAAIDNkLgFAAAAAAAAAJshcQsAAAAAAAAANkPiFgAAAAAAAABshsQtAAAAAAAAANgMiVsAAAAAAAAAsBkStwAAAAAAAABgMyRuAQAAAAAAAMBmSNwCAAAAAAAAgM2QuAUAAAAAAAAAmyFxCwAAAAAAAAA2Q+IWAAAAAAAAAGyGxC0AAAAAAAAA2AyJWwAAAAAAAACwGRK3AAAAAAAAAGAzJG4BAAAAAAAAwGZI3AIAAAAAAACAzZC4BQAAAAAAAACbIXELAAAAAAAAADZD4hYAAAAAAAAAbIbELQAAAAAAAADYDIlbAAAAAAAAALAZErcAAAAAAAAAYDMkbgEAAAAAAADAZkjcAgAAAAAAAIDNkLgFAAAAAAAAAJshcQsAAAAAAAAANkPiFgAAAAAAAABshsQtAAAAAAAAANgMiVsAAAAAAAAAsBkStwAAAAAAAABgMyRuAQAAAAAAAMBmSNwCAAAAAAAAgM2QuAUAAAAAAAAAmyFxCwAAAAAAAAA2Q+IWAAAAAAAAAGyGxC0AAAAAAAAA2AyJWwAAAAAAAACwGRK3AAAAAAAAAGAztkjcTps2TfXr11dQUJA6dOigNWvW+DokAABKnaf94SeffKKmTZsqKChIV111lb766qtSihQAAHuiLwUAlCc+T9zOnz9fI0eO1KhRo7Rhwwa1bNlSCQkJSklJ8XVoAACUGk/7wx9++EF/+9vfdP/992vjxo3q3bu3evfurS1btpRy5AAA2AN9KQCgvPF54nbSpEkaNGiQ7rvvPl1xxRWaPn26QkJCNHPmTF+HBgBAqfG0P3zttdfUo0cPPf7442rWrJnGjBmjNm3aaOrUqaUcOQAA9kBfCgAobwJ8efHTp09r/fr1SkxMdO3z8/NTt27dtGrVqgLn5+TkKCcnx7WdlpYmSUpNTZXT6SxyHA6HQ7m5ufptT5KyMjOKXM7FnDzym7KzsrR161alp6d75RqecjgcSk5O9nUYpeLAgQPKzs4u0/f44MGDOp2To992Jykrw3HpJxij4LxsZfkfkizrkqfbsY16ytv32Y6vkd3exx63Uw+dPHJIubm5cjgcSk1NLXI5+ffPGFNCkRWPp/2hJK1atUojR45025eQkKCFCxde8Dre6kczMjJ0Ji9Pe7PO6FSed17To2ecOn3aqRO/nFDGoUu/v0+nndaptFNasmSJtm7dWuLxGGOUl5cnf39/WYX4O9Zujhw5ooz0DDl/cRbq9ZQkS5aCI4J1JP2IjEr+Pnv7nh05ckSOzExtdDp1JOPSdc7OzdXB0GApM1d+fpe+x562UU+l70uXLEt7s53KTs8t8fIl6ahTys05o4xD2TqTfeaS52edyFJudq6t+kVPebMfPXjwoHJP5yrjtwzlZed55Rqeyj6Rraq5VctdPyqVTl/qrX40PT1dzjynrdoK/pB1IkvOPKfS09OL9b65lPT0dJ1xOrUtI0PpebQDuzmUlaUzztJpB07nGWVkbFNeXtnsW8uzrKxDcjrPFLsdeNSPGh86dOiQkWR++OEHt/2PP/64ad++fYHzR40aZSTx4MGDBw8eJfI4ePBgaXV5F+Vpf2iMMZUqVTLz5s1z2zdt2jQTExNzwevQj/LgwYMHj5J82KUfNaZ0+lL6UR48ePDgUZKPwvSjPh1x66nExES3fxF1Op06ceKEqlWrVqzRLunp6YqLi9PBgwcVERFREqHaXkWrc0Wrr0SdK0KdK1p9pZKrszFGDodDtWrVKsHo7I9+tORQ5/Jf54pWX6ni1bmi1VeiHy0ub/Wj5U1FfG+hINoBJNrBhXjSj/o0cVu9enX5+/vryJEjbvuPHDmi2NjYAucHBgYqMDDQbV+VKlVKLJ6IiIgK15AqWp0rWn0l6lwRVLT6SiVT58jIyBKKpvg87Q8lKTY21qPzJfpRb6DO5V9Fq69U8epc0eorlb9+VCqdvtTb/Wh5UxHfWyiIdgCJdnA+he1Hfbo4WeXKldW2bVstXrzYtc/pdGrx4sWKj4/3YWQAAJSeovSH8fHxbudL0qJFi+g/AQAVEn0pAKA88vlUCSNHjlT//v3Vrl07tW/fXlOmTFFmZqbuu+8+X4cGAECpuVR/2K9fP9WuXVvjxo2TJA0fPlydO3fWxIkTddNNN+mjjz7SunXr9M477/iyGgAA+Ax9KQCgvPF54vauu+7S0aNH9dxzz+nw4cNq1aqVvvnmG9WoUaPUYggMDNSoUaMK/OylPKtoda5o9ZWoc0VQ0eorle86X6o/PHDggPz8/vihTMeOHTVv3jw988wz+t///V81btxYCxcu1JVXXlnqsZfn+3Ih1Ln8q2j1lSpenStafaXyX+ey3JeWJ+W9naFwaAeQaAclwTLGGF8HAQAAAAAAAAD4g0/nuAUAAAAAAAAAFETiFgAAAAAAAABshsQtAAAAAAAAANgMiVsAAAAAAFCivv/+e1mWpdTU1IueV79+fU2ZMqVUYoJnSuveWJalhQsXXvSce++9V7179/Z6LLCvLl266JFHHnFtV5S/OypU4rZ+/fqyLKvA46GHHnKds2rVKl133XUKDQ1VRESEOnXqpKysLB9GXXQXq+++ffvOe8yyLH3yySe+Dr3ILnWPDx8+rHvuuUexsbEKDQ1VmzZt9Omnn/o46qK7VH13796tW2+9VdHR0YqIiFCfPn105MgRH0ddPHl5eXr22WfVoEEDBQcHq1GjRhozZozOXWfRGKPnnntONWvWVHBwsLp166adO3f6MOqiK0x9FyxYoO7du6tatWqyLEubNm3yXcAl4FJ1zs3N1ZNPPqmrrrpKoaGhqlWrlvr166fffvvNx5GXf/Sj9KP0o/SjZQ39KP0oCrpQAqywidaimj17tqpUqeKVsuHuzwmufGXhHuR/xirrfxeXRQcPHtSAAQNUq1YtVa5cWfXq1dPw4cN1/PhxX4dWoVWoxO3atWuVnJzseixatEiSdOedd0o6+2WzR48e6t69u9asWaO1a9dq6NCh8vMrmy/TxeobFxfndiw5OVmjR49WWFiYbrzxRh9HXnSXusf9+vVTUlKSPv/8c/3888+67bbb1KdPH23cuNGXYRfZxeqbmZmp7t27y7IsLVmyRCtXrtTp06fVq1cvOZ1OH0dedK+88oreeustTZ06Vdu2bdMrr7yiV199VW+88YbrnFdffVWvv/66pk+frh9//FGhoaFKSEhQdna2DyMvmsLUNzMzU9dcc41eeeUVH0Zaci5V51OnTmnDhg169tlntWHDBi1YsEBJSUm6+eabfRx5+Uc/Sj9KP0o/WtbQj9KPAgAubc+ePWrXrp127typDz/8ULt27dL06dO1ePFixcfH68SJE167dm5urtfKLhdMBTZ8+HDTqFEj43Q6jTHGdOjQwTzzzDM+jsp7/lzfP2vVqpUZMGBAKUflXX+uc2hoqHn//ffdzomKijLvvvuuL8IrcefW99tvvzV+fn4mLS3NdTw1NdVYlmUWLVrkwyiL56abbirQTm+77TbTt29fY4wxTqfTxMbGmvHjx7uOp6ammsDAQPPhhx+Waqwl4VL1PdfevXuNJLNx48ZSis47PKlzvjVr1hhJZv/+/d4OD+egH3VHP1r20Y/Sj9KP0o9WBP379ze33HJLgf1Lly41kszJkyeNMcYsX77cXHPNNSYoKMjUqVPHDBs2zGRkZLjOf//9903btm1NWFiYqVGjhvnb3/5mjhw5ct7y8v987mPUqFHGGGPq1atnxo4da+677z4TFhZm4uLizNtvv+0qp2vXruahhx5yizUlJcVUqlTJfPfddyX3wpQjnTt3NsOHDy+wf9asWSYyMtIY80c7GD9+vImNjTVRUVHmwQcfNKdPn3adf6l7Y4wxBw4cMHfeeaeJjIw0VatWNTfffLPZu3ev6/iaNWtMt27dTLVq1UxERITp1KmTWb9+vVsZksxnn33m+vO5j86dOxcq3tGjR5vmzZsXqHPLli3L9efTktKjRw9Tp04dc+rUKbf9ycnJJiQkxAwePNgkJiaa9u3bF3huixYtzOjRo13b7777rmnatKkJDAw0TZo0MdOmTXMdy+9rP/roI9OpUycTGBhoZs2aZY4dO2buvvtuU6tWLRMcHGyuvPJKM2/ePLfr/Lld16tXz0yePLlkXgAbK5tDYErA6dOn9cEHH2jAgAGyLEspKSn68ccfFRMTo44dO6pGjRrq3LmzVqxY4etQS8Sf6/tn69ev16ZNm3T//ff7IDrvOF+dO3bsqPnz5+vEiRNyOp366KOPlJ2drS5duvg22BLw5/rm5OTIsiwFBga6zgkKCpKfn1+ZbtcdO3bU4sWLtWPHDknS5s2btWLFCtcIt7179+rw4cPq1q2b6zmRkZHq0KGDVq1a5ZOYi+NS9S2PilLntLQ0WZZl+59+lSf0o+7oR7v4NtgSQD9KP1pe0I+iJOzevVs9evTQ7bffrp9++knz58/XihUrNHToUNc5ubm5GjNmjDZv3qyFCxdq3759uvfee89bXseOHTVlyhRFRES4fuXw2GOPuY5PnDhR7dq108aNG/Xggw9qyJAhSkpKkiQNHDhQ8+bNU05Ojuv8Dz74QLVr19Z1113nnReggli6dKl2796tpUuXas6cOZo9e7Zmz57tds7F7k1ubq4SEhIUHh6u5cuXa+XKlQoLC1OPHj10+vRpSZLD4VD//v21YsUKrV69Wo0bN1bPnj3lcDjOG9OaNWskSd99952Sk5O1YMGCQsU7YMAAbdu2TWvXrnWdv3HjRv3000+67777SuolK5dOnDihb7/9Vg8++KCCg4PdjsXGxqpv376aP3+++vbtqzVr1mj37t2u41u3btVPP/2kv//975KkuXPn6rnnntPYsWO1bds2vfTSS3r22Wc1Z84ct3KfeuopDR8+XNu2bXP9qqdt27b68ssvtWXLFv3zn//UPffc42oPFZqvM8e+Mn/+fOPv728OHTpkjDFm1apVRpKJiooyM2fONBs2bDCPPPKIqVy5stmxY4ePoy2+P9f3z4YMGWKaNWtWylF51/nqfPLkSdO9e3cjyQQEBJiIiAjz7bff+jDKkvPn+qakpJiIiAgzfPhwk5mZaTIyMszQoUONJPPPf/7Tx9EWXV5ennnyySeNZVkmICDAWJZlXnrpJdfxlStXGknmt99+c3venXfeafr06VPa4Rbbpep7rvIyUsiTOhtjTFZWlmnTpo35+9//XopRgn7UHf1o2Uc/ehb9KP0oyr/+/fsbf39/Exoa6vYICgpyjZC9//77C/xdt3z5cuPn52eysrLOW+7atWuNJONwOIwxBUfwnjva81z16tUz//jHP1zbTqfTxMTEmLfeessYc7aNVq1a1cyfP991TosWLczzzz9fnJehXCvsiNt69eqZM2fOuI7feeed5q677nJtX+re/Otf/zJNmjRx+zVSTk6OCQ4OvuDng7y8PBMeHm7+85//uPbpnBG3F/q7uDDx3njjjWbIkCGu7WHDhpkuXbqcNw78YfXq1W734M8mTZpkJJkjR46Yli1bmhdeeMF1LDEx0XTo0MG13ahRowIjZceMGWPi4+ONMX/c3ylTplwyrptuusk8+uijrm1G3FYw7733nm688UbVqlVLklxzlT3wwAO677771Lp1a02ePFlNmjTRzJkzfRlqifhzfc+VlZWlefPmlatRQtL56/zss88qNTVV3333ndatW6eRI0eqT58++vnnn30Yacn4c32jo6P1ySef6D//+Y/CwsIUGRmp1NRUtWnTpszONylJH3/8sebOnat58+Zpw4YNmjNnjiZMmFDgX/DKi4pWX8mzOufm5qpPnz4yxuitt97yQbQVF/3oH+hH6UfLkorWr1S0+kr0oyicrl27atOmTW6PGTNmuI5v3rxZs2fPVlhYmOuRkJAgp9OpvXv3Sjr7a5NevXqpbt26Cg8PV+fOnSVJBw4c8DieFi1auP5sWZZiY2OVkpIi6eyvHe655x7X54kNGzZoy5YtFxzdi8Jr3ry5/P39Xds1a9Z0ve75LnZvNm/erF27dik8PNzVTqKiopSdne0alXnkyBENGjRIjRs3VmRkpCIiIpSRkVGkdnKpeAcNGqQPP/xQ2dnZOn36tObNm6cBAwZ4fJ2KypyzcOeF9O3bV/PmzXOd/+GHH6pv376Szs4Zv3v3bt1///1uf3e8+OKLbqN0Jaldu3Zu23l5eRozZoyuuuoqRUVFKSwsTN9++22R2kl5E+DrAHxh//79+u6779yG3NesWVOSdMUVV7id26xZszLfUM5X33P9+9//1qlTp9SvX79Sjsx7zlfn3bt3a+rUqdqyZYuaN28uSWrZsqWWL1+uadOmafr06b4Kt9gudI+7d++u3bt369ixYwoICFCVKlUUGxurhg0b+ijS4nv88cf11FNP6e6775YkXXXVVdq/f7/GjRun/v37KzY2VtLZDwj57+v87VatWvki5GK5VH3Lo8LWOf/L5v79+7VkyRJFRET4KuQKh37UHf0o/WhZQj9KP5qPfrRiCw0N1WWXXea279dff3X9OSMjQw888IAefvjhAs+tW7euMjMzlZCQoISEBM2dO1fR0dE6cOCAEhISXD+R90SlSpXcti3LclsIcuDAgWrVqpV+/fVXzZo1S9ddd53q1avn8XUqioiICKWlpRXYn5qaqsjISNf2pV73S52TkZGhtm3bau7cuQWuFR0dLUnq37+/jh8/rtdee0316tVTYGCg4uPjvdJOevXqpcDAQH322WeqXLmycnNzdccdd3h8nYrmsssuk2VZ2rZtm2699dYCx7dt26aqVasqOjpaf/vb3/Tkk09qw4YNysrK0sGDB3XXXXdJOtseJOndd99Vhw4d3Mo4N+Eunf076Fzjx4/Xa6+9pilTpuiqq65SaGioHnnkkSK1k/KmQiZuZ82apZiYGN10002uffXr11etWrVcc7Xk27FjR5mfA+t89T3Xe++9p5tvvtn1F2t5cL46nzp1SpIKjJLx9/cv06tDS5e+x9WrV5ckLVmyRCkpKWV61eBTp05d9B42aNBAsbGxWrx4sesLZnp6un788UcNGTKktMMttkvVtzwqTJ3zv2zu3LlTS5cuVbVq1Uo7zAqNftQd/WjZ/zuJfpR+tDyhH0VJaNOmjX755ZcCyd18P//8s44fP66XX35ZcXFxkqR169ZdtMzKlSsrLy+vSPFcddVVateund59913NmzdPU6dOLVI5FUWTJk30f//3fwX2b9iwQZdffnmJXadNmzaaP3++YmJiLviPPytXrtSbb76pnj17SpIOHjyoY8eOXbDMypUrS1KR2kpAQID69++vWbNmqXLlyrr77rsLzNmKgqpVq6YbbrhBb775pkaMGOH2mh0+fFhz585Vv379ZFmW6tSpo86dO2vu3LnKysrSDTfcoJiYGElSjRo1VKtWLe3Zs8c1CrewVq5cqVtuuUX/+Mc/JJ39Nd+OHTsKDAqpiCpc4tbpdGrWrFnq37+/AgL+qL5lWXr88cc1atQotWzZUq1atdKcOXO0fft2/fvf//ZhxMVzofrm27Vrl5YtW6avvvrKB9F5x4Xq3LRpU1122WV64IEHNGHCBFWrVk0LFy7UokWL9MUXX/gw4uK52D2eNWuWmjVrpujoaK1atUrDhw/XiBEj1KRJEx9FW3y9evXS2LFjVbduXTVv3lwbN27UpEmTXD+BsSxLjzzyiF588UU1btxYDRo00LPPPqtatWqpd+/evg2+CC5VX+nsZPIHDhzQb7/9JkmuxFlsbKxr5FRZcqk65//L+YYNG/TFF18oLy9Phw8fliRFRUW5PuzBO+hH3dGP0o+WNfSj9KP0oyiMJ598UldffbWGDh2qgQMHKjQ0VL/88osWLVqkqVOnqm7duqpcubLeeOMNDR48WFu2bNGYMWMuWmb9+vWVkZGhxYsXq2XLlgoJCVFISEihYxo4cKCGDh2q0NDQ844KxB+GDBmiqVOn6uGHH9bAgQMVGBioL7/8Uh9++KH+85//lNh1+vbtq/Hjx+uWW27RCy+8oDp16mj//v1asGCBnnjiCdWpU0eNGzfWv/71L7Vr107p6el6/PHHL5pMjYmJUXBwsL755hvVqVNHQUFBbqOEL2XgwIFq1qyZpLPJQBTO1KlT1bFjRyUkJOjFF19UgwYNtHXrVj3++OOqXbu2xo4d6zq3b9++GjVqlE6fPq3Jkye7lTN69Gg9/PDDioyMVI8ePZSTk6N169bp5MmTGjly5AWv37hxY/373//WDz/8oKpVq2rSpEk6cuQIiVup4i1O9u233xpJJikp6bzHx40bZ+rUqWNCQkJMfHy8Wb58eSlHWLIuVd/ExEQTFxdn8vLySjky77lYnXfs2GFuu+02ExMTY0JCQkyLFi3M+++/74MoS87F6vvkk0+aGjVqmEqVKpnGjRubiRMnuk0cXxalp6eb4cOHm7p165qgoCDTsGFD8/TTT5ucnBzXOU6n0zz77LOmRo0aJjAw0Fx//fUXfA/YXWHqO2vWLCOpwGPUqFG+C7wYLlXn/Antz/dYunSpb4OvAOhH3dGP0o+WNfSj9KP0o+jfv7+55ZZbCuz/82Jia9asMTfccIMJCwszoaGhpkWLFmbs2LGu8+fNm2fq169vAgMDTXx8vPn888/dFpX6c3nGGDN48GBTrVo1t/fY+RYYatmyZYH3oMPhMCEhIebBBx8s5itQMeTfv+joaBMZGWk6dOjgtvjU+drB8OHDTefOnV3bhbk3ycnJpl+/fqZ69eomMDDQNGzY0AwaNMikpaUZY4zZsGGDadeunQkKCjKNGzc2n3zySYFy9aeFsd59910TFxdn/Pz8XPEUJt581157rWnevPklXiH82b59+0z//v1dn33i4uLMsGHDzLFjx9zOO3nypAkMDDQhISGuxQjPNXfuXNOqVStTuXJlU7VqVdOpUyezYMECY8yFF587fvy4ueWWW0xYWJiJiYkxzzzzjOnXr5/bPa+oi5NZxhRi9mEAAAAAAAAf2bdvnxo1aqS1a9eqTZs2vg4HNmWMUePGjfXggw9edIQnUFZUuKkSAAAAAABA2ZCbm6vjx4/rmWee0dVXX03SFhd09OhRffTRRzp8+LDuu+8+X4cDlAgStwAAAAAAwJZWrlyprl276vLLLy/T8+bD+2JiYlS9enW98847qlq1qq/DAUoEUyUAAAAAAAAAgM34+ToAAAAAAAAAAIA7ErcAAAAAAAAAYDMkbgEAAAAAAADAZkjcAgAAAAAAAIDNkLgFAAAAAAAAAJshcQug1FiWpYULF3r8vKSkJMXGxsrhcEiSZs+erSpVqhQ7nqeeekrDhg0rdjnl0b59+3T//ferQYMGCg4OVqNGjTRq1CidPn36os/bvXu3br31VkVHRysiIkJ9+vTRkSNH3M45ceKE+vbtq4iICFWpUkX333+/MjIy3K5tWVaBx+rVqz2qQ/369QuU8fLLL3tUBgDYCf1o2UE/CgAASgKJW5QpR48e1ZAhQ1S3bl0FBgYqNjZWCQkJWrlypa9Ds42ifqkrSc8//7xatWpVYuUlJiZq2LBhCg8PL7EyJemxxx7TnDlztGfPnhIttyzp0qWLZs+eXWD/9u3b5XQ69fbbb2vr1q2aPHmypk+frv/93/+9YFmZmZnq3r27LMvSkiVLtHLlSp0+fVq9evWS0+l0nde3b19t3bpVixYt0hdffKFly5bpn//8Z4HyvvvuOyUnJ7sebdu29bh+L7zwglsZJBhQ0dGPXhr9aOHRj9KPAgAA7wrwdQCAJ26//XadPn1ac+bMUcOGDXXkyBEtXrxYx48f93Vo8JIDBw7oiy++0BtvvFHiZVevXl0JCQl66623NH78+BIvvyzr0aOHevTo4dpu2LChkpKS9NZbb2nChAnnfc7KlSu1b98+bdy4UREREZKkOXPmqGrVqlqyZIm6deumbdu26ZtvvtHatWvVrl07SdIbb7yhnj17asKECapVq5arvGrVqik2NvaCMc6YMUMTJ07U3r17Vb9+fT388MN68MEH3c4JDw+/aBlARUM/WvHQj/oG/SgAACgJjLhFmZGamqrly5frlVdeUdeuXVWvXj21b99eiYmJuvnmm93OGzhwoOsnZtddd502b97sVtbLL7+sGjVqKDw8XPfff7+eeuopt5EtXbp00SOPPOL2nN69e+vee+91befk5Oixxx5T7dq1FRoaqg4dOuj77793Hc//GeK3336rZs2aKSwsTD169FBycrJbuTNnzlTz5s0VGBiomjVraujQoR7VxVMzZsxQs2bNFBQUpKZNm+rNN990Hcv/ad2CBQvUtWtXhYSEqGXLllq1apVbGe+++67i4uIUEhKiW2+9VZMmTXL95HL27NkaPXq0Nm/e7PpZ3bkjUY4dO6Zbb71VISEhaty4sT7//POLxvvxxx+rZcuWql27doFjCxcuVOPGjRUUFKSEhAQdPHjQdSx/tNLbb7/tirVPnz5KS0tzK6NXr1766KOPCvvyVWhpaWmKioq64PGcnBxZlqXAwEDXvqCgIPn5+WnFihWSpFWrVqlKlSquL5uS1K1bN/n5+enHH390K+/mm29WTEyMrrnmmgLtZO7cuXruuec0duxYbdu2TS+99JKeffZZzZkzx+28l19+WdWqVVPr1q01fvx4nTlzpsj1B8o6+lH60T+jHy1d9KMAAMBjBigjcnNzTVhYmHnkkUdMdnb2Bc/r1q2b6dWrl1m7dq3ZsWOHefTRR021atXM8ePHjTHGzJ8/3wQGBpoZM2aY7du3m6efftqEh4ebli1busro3LmzGT58uFu5t9xyi+nfv79re+DAgaZjx45m2bJlZteuXWb8+PEmMDDQ7NixwxhjzKxZs0ylSpVMt27dzNq1a8369etNs2bNzN///ndXGW+++aYJCgoyU6ZMMUlJSWbNmjVm8uTJha7L+Ugyn3322XmPffDBB6ZmzZrm008/NXv27DGffvqpiYqKMrNnzzbGGLN3714jyTRt2tR88cUXJikpydxxxx2mXr16Jjc31xhjzIoVK4yfn58ZP368SUpKMtOmTTNRUVEmMjLSGGPMqVOnzKOPPmqaN29ukpOTTXJysjl16pQrtjp16ph58+aZnTt3mocfftiEhYVdtD4333yzGTx4sNu+/Ne2Xbt25ocffjDr1q0z7du3Nx07dnSdM2rUKBMaGmquu+46s3HjRvPf//7XXHbZZW6vvzHGbNu2zUgye/fuvWAM5Vnnzp3NrFmzLnnezp07TUREhHnnnXcueE5KSoqJiIgww4cPN5mZmSYjI8MMHTrUSDL//Oc/jTHGjB071lx++eUFnhsdHW3efPNNY4wxR48eNRMnTjSrV682a9asMU8++aSxLMv8v//3/1znN2rUyMybN8+tjDFjxpj4+HjX9sSJE83SpUvN5s2bzVtvvWWqVKliRowYccm6AuUV/Sj9aD760ZJDPwoAALyJxC3KlH//+9+matWqJigoyHTs2NEkJiaazZs3u44vX77cREREFPhC2qhRI/P2228bY4yJj483Dz74oNvxDh06ePSFc//+/cbf398cOnTI7Zzrr7/eJCYmGmPOfimSZHbt2uU6Pm3aNFOjRg3Xdq1atczTTz993roWpi7nc7EvnJf6kJ7/hXPGjBmu41u3bjWSzLZt24wxxtx1113mpptuciujb9++ri+cxpz9snfu63lubM8884xrOyMjw0gyX3/99QXr07JlS/PCCy+47ct/bVevXu3al//F8ccff3TF4O/vb3799VfXOV9//bXx8/MzycnJrn1paWlGkvn+++8vGEN5MnbsWBMaGup6+Pn5mcDAQLd9+/fvd3vOr7/+aho1amTuv//+S5b/7bffmoYNGxrLsoy/v7/5xz/+Ydq0aeNKGhTmC+f53HPPPeaaa64xxvzRboKDg93iDgwMNDExMRcs47333jMBAQEXTVgB5R39KP2oMfSjxUE/Sj8KAEBpYo5blCm33367brrpJi1fvlyrV6/W119/rVdffVUzZszQvffeq82bNysjI0PVqlVze15WVpZ2794tSdq2bZsGDx7sdjw+Pl5Lly4tdBw///yz8vLydPnll7vtz8nJcbt2SEiIGjVq5NquWbOmUlJSJEkpKSn67bffdP3115/3GoWpiycyMzO1e/du3X///Ro0aJBr/5kzZxQZGel2bosWLdxizo+3adOmSkpK0q233up2fvv27fXFF18UKo5zyw4NDVVERITrNTmfrKwsBQUFFdgfEBCgv/zlL67tpk2bqkqVKtq2bZvat28vSapbt67bT0Pj4+PldDpdq2tLUnBwsCTp1KlThYq/rBs8eLD69Onj2u7bt69uv/123Xbbba59586P99tvv6lr167q2LGj3nnnnUuW3717d+3evVvHjh1TQECAqlSpotjYWDVs2FCSFBsbW+B+nzlzRidOnLjoHHodOnTQokWLJMm1cva7776rDh06uJ3n7+9/0TLOnDmjffv2qUmTJpesC1Ae0Y/Sj+ajHy0a+lH6UQAAShOJW5Q5QUFBuuGGG3TDDTfo2Wef1cCBAzVq1Cjde++9ysjIUM2aNd3myMuXP3dcYfj5+ckY47YvNzfX9eeMjAz5+/tr/fr1BT7ghoWFuf5cqVIlt2OWZbnKzf+icyElVZdzy5MK9yH93Lgty5Ikt9WMi+N8r8nFyq5evbpOnjxZItc+nxMnTkiSoqOjvXYNO4mKinKbXy84OFgxMTG67LLLCpx76NAhde3aVW3bttWsWbPk51f4adGrV68uSVqyZIlSUlJc82fGx8crNTVV69evd61uvWTJEjmdzgLt8lybNm1yJT9q1KihWrVqac+ePerbt2+hY9q0aZP8/PwUExNT6OcA5RH9KP1oSaIfpR8FAADeQ+IWZd4VV1yhhQsXSpLatGmjw4cPKyAgQPXr1z/v+c2aNdOPP/6ofv36ufatXr3a7Zzo6Gi3xU/y8vK0ZcsWde3aVZLUunVr5eXlKSUlRddee22R4g4PD1f9+vW1ePFiV7nnKkxdPFHUD+l/1qRJE61du9Zt35+3K1eurLy8vCJf41ytW7fWL7/8UmD/mTNntG7dOteooKSkJKWmpqpZs2aucw4cOKDffvvNNfJl9erV8vPzcxslsmXLFlWqVEnNmzcvkXjLi0OHDqlLly6qV6+eJkyYoKNHj7qO5Y/oOXTokK6//nq9//77rvswa9YsNWvWTNHR0Vq1apWGDx+uESNGuF7zZs2aqUePHho0aJCmT5+u3NxcDR06VHfffbfrPs2ZM0eVK1dW69atJUkLFizQzJkzNWPGDFcMo0eP1sMPP6zIyEj16NFDOTk5WrdunU6ePKmRI0dq1apV+vHHH9W1a1eFh4dr1apVGjFihP7xj3+oatWqpfIaAmUF/Wjh0I/Sj3qCfhQAAJQEErcoM44fP64777xTAwYMUIsWLRQeHq5169bp1Vdf1S233CLp7Kq68fHx6t27t1599VVdfvnl+u233/Tll1/q1ltvVbt27TR8+HDde++9ateunf76179q7ty52rp1q+snaJJ03XXXaeTIkfryyy/VqFEjTZo0Sampqa7jl19+ufr27at+/fpp4sSJat26tY4eParFixerRYsWuummmwpVp+eff16DBw9WTEyMbrzxRjkcDq1cuVLDhg0rVF0uZO/evdq0aZPbvsaNG1/yQ3phDBs2TJ06ddKkSZPUq1cvLVmyRF9//bVrRJEk1a9f3xVDnTp1FB4e7rZCsicSEhI0cOBA5eXluY1oqlSpkoYNG6bXX39dAQEBGjp0qK6++mrXFx/p7Kiy/v37a8KECUpPT9fDDz+sPn36uP2UcPny5br22msvOXKrolm0aJF27dqlXbt2qU6dOm7H8ke75ebmKikpye3nsUlJSUpMTNSJEydUv359Pf300xoxYoTb8+fOnauhQ4fq+uuvl5+fn26//Xa9/vrrbueMGTNG+/fvV0BAgJo2bar58+frjjvucB0fOHCgQkJCNH78eD3++OMKDQ3VVVdd5VrFPjAwUB999JGef/555eTkqEGDBhoxYkSh2zlQHtGP0o/Sj5Ye+lEAAFAifDnBLuCJ7Oxs89RTT5k2bdqYyMhIExISYpo0aWKeeeYZ12rLxhiTnp5uhg0bZmrVqmUqVapk4uLiTN++fc2BAwdc54wdO9ZUr17dhIWFmf79+5snnnjCbRGQ06dPmyFDhpioqCgTExNjxo0bV2A17NOnT5vnnnvO1K9f31SqVMnUrFnT3Hrrreann34yxpxd+OPchUaMMeazzz4zf37bTZ8+3TRp0sRVxrBhwzyqy59JOu9j+fLlxhhj5s6da1q1amUqV65sqlatajp16mQWLFhgjPljUZWNGze6yjt58qSRZJYuXera984775jatWub4OBg07t3b/Piiy+a2NhYt3t1++23mypVqhhJrtWWdZ4FXyIjIy+6GnNubq6pVauW+eabb1z78l/bTz/91DRs2NAEBgaabt26uS0Gkr+wy5tvvmlq1aplgoKCzB133GFOnDjhVn6TJk3Mhx9+eMHrA0B5QT9KP5qPfhQAAKBssIz50wRkQAX0/PPPa+HChQVG16BwBg0apO3bt2v58uVeKX/atGn6/PPP9e233xb6OYW5p19//bUeffRR/fTTTwoI4AcIAFBU9KPFQz8KAACA8+ETFgCPTZgwQTfccINCQ0P19ddfa86cOXrzzTe9dr0HHnhAqampcjgcCg8PL7FyMzMzNWvWLL5sAgBKFf0oAAAACoNPWQA8tmbNGr366qtyOBxq2LChXn/9dQ0cONBr1wsICNDTTz9d4uWeO9cbAAClhX4UAAAAhcFUCQAAAAAAAABgM36+DgAAAAAAAAAA4I7ELQAAAAAAAADYDIlbAAAAAAAAALAZErcAAAAAAAAAYDMkbgEAAAAAAADAZkjcAgAAAAAAAIDNkLgFAAAAAAAAAJshcQsAAAAAAAAANvP/Acl0SlHdBJ+QAAAAAElFTkSuQmCC\n"
          },
          "metadata": {}
        },
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "\n",
            "🔍 Detailed APP Gene Analysis:\n",
            "   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n",
            "   📊 SEQUENCE STATISTICS:\n",
            "      Total APP sequences: 13\n",
            "      Healthy sequences: 7\n",
            "      Unhealthy sequences: 6\n",
            "      Overall average length: 290,578 bp\n",
            "      Length range: 290,576 - 290,582 bp\n",
            "      Total nucleotides: 3,777,516 bp\n",
            "      Healthy avg length: 290,579 bp\n",
            "      Unhealthy avg length: 290,578 bp\n",
            "\n",
            "============================================================\n",
            "⚙️  CONFIGURING MOTIF DISCOVERY PARAMETERS\n",
            "============================================================\n",
            "🎯 Motif Discovery Configuration:\n",
            "   Target: APP gene motifs\n",
            "   Motif length: 8 bp\n",
            "   Population size: 30\n",
            "   Max generations: 50\n",
            "   Mutation rate: 0.1\n",
            "   Convergence threshold: 0.0001\n",
            "\n",
            "📊 Analysis Dataset:\n",
            "   Using all 13 APP gene sequences\n",
            "   Healthy genes: 7\n",
            "   Unhealthy genes: 6\n",
            "   Note: Full gene lengths preserved for comprehensive motif discovery\n"
          ]
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Genetic Algorithm Implementation\n",
        "class Individual:\n",
        "    \"\"\"Individual in GA population representing motif positions\"\"\"\n",
        "\n",
        "    def __init__(self, sequences: List[str], motif_length: int):\n",
        "        self.sequences = sequences\n",
        "        self.motif_length = motif_length\n",
        "        self.positions = self._initialize_positions()\n",
        "        self.fitness = 0.0\n",
        "        self.motif = \"\"\n",
        "        self.pwm = None\n",
        "\n",
        "    def _initialize_positions(self) -> List[int]:\n",
        "        \"\"\"Initialize random positions for each sequence\"\"\"\n",
        "        positions = []\n",
        "        for seq in self.sequences:\n",
        "            if len(seq) >= self.motif_length:\n",
        "                max_pos = len(seq) - self.motif_length\n",
        "                positions.append(random.randint(0, max_pos))\n",
        "            else:\n",
        "                positions.append(0)\n",
        "        return positions\n",
        "\n",
        "    def calculate_fitness(self) -> float:\n",
        "        \"\"\"Calculate fitness based on motif conservation and information content\"\"\"\n",
        "        # Extract motif instances\n",
        "        motif_instances = []\n",
        "        for i, pos in enumerate(self.positions):\n",
        "            if pos + self.motif_length <= len(self.sequences[i]):\n",
        "                instance = self.sequences[i][pos:pos + self.motif_length]\n",
        "                motif_instances.append(instance)\n",
        "\n",
        "        if not motif_instances:\n",
        "            self.fitness = 0.0\n",
        "            return self.fitness\n",
        "\n",
        "        # Calculate Position Weight Matrix\n",
        "        self.pwm = self._calculate_pwm(motif_instances)\n",
        "\n",
        "        # Calculate information content\n",
        "        ic = self._calculate_information_content(self.pwm)\n",
        "\n",
        "        # Calculate conservation score\n",
        "        conservation = self._calculate_conservation_score(motif_instances)\n",
        "\n",
        "        # Get consensus motif\n",
        "        self.motif = self._get_consensus(motif_instances)\n",
        "\n",
        "        # Combined fitness: IC * conservation * length_penalty\n",
        "        length_penalty = len(motif_instances) / len(self.sequences)\n",
        "        self.fitness = ic * conservation * length_penalty\n",
        "\n",
        "        return self.fitness\n",
        "\n",
        "    def _calculate_pwm(self, motif_instances: List[str]) -> np.ndarray:\n",
        "        \"\"\"Calculate Position Weight Matrix\"\"\"\n",
        "        if not motif_instances:\n",
        "            return np.ones((4, self.motif_length)) * 0.25\n",
        "\n",
        "        pwm = np.zeros((4, self.motif_length))\n",
        "        nucleotide_to_index = {'A': 0, 'T': 1, 'G': 2, 'C': 3}\n",
        "        pseudocount = 0.001\n",
        "\n",
        "        for pos in range(self.motif_length):\n",
        "            for motif in motif_instances:\n",
        "                if pos < len(motif) and motif[pos] in nucleotide_to_index:\n",
        "                    pwm[nucleotide_to_index[motif[pos]], pos] += 1\n",
        "\n",
        "            # Add pseudocounts and normalize\n",
        "            pwm[:, pos] += pseudocount\n",
        "            pwm[:, pos] /= np.sum(pwm[:, pos])\n",
        "\n",
        "        return pwm\n",
        "\n",
        "    def _calculate_information_content(self, pwm: np.ndarray) -> float:\n",
        "        \"\"\"Calculate information content\"\"\"\n",
        "        ic_total = 0.0\n",
        "        for pos in range(pwm.shape[1]):\n",
        "            ic_pos = 0.0\n",
        "            for nuc_prob in pwm[:, pos]:\n",
        "                if nuc_prob > 0:\n",
        "                    ic_pos += nuc_prob * np.log2(nuc_prob / 0.25)\n",
        "            ic_total += ic_pos\n",
        "        return ic_total\n",
        "\n",
        "    def _calculate_conservation_score(self, motif_instances: List[str]) -> float:\n",
        "        \"\"\"Calculate conservation score based on sequence similarity\"\"\"\n",
        "        if len(motif_instances) < 2:\n",
        "            return 0.0\n",
        "\n",
        "        total_similarity = 0.0\n",
        "        comparisons = 0\n",
        "\n",
        "        for i in range(len(motif_instances)):\n",
        "            for j in range(i + 1, len(motif_instances)):\n",
        "                seq1, seq2 = motif_instances[i], motif_instances[j]\n",
        "                if len(seq1) == len(seq2):\n",
        "                    similarity = sum(1 for a, b in zip(seq1, seq2) if a == b) / len(seq1)\n",
        "                    total_similarity += similarity\n",
        "                    comparisons += 1\n",
        "\n",
        "        return total_similarity / comparisons if comparisons > 0 else 0.0\n",
        "\n",
        "    def _get_consensus(self, motif_instances: List[str]) -> str:\n",
        "        \"\"\"Get consensus sequence\"\"\"\n",
        "        if not motif_instances:\n",
        "            return \"A\" * self.motif_length\n",
        "\n",
        "        consensus = \"\"\n",
        "        for pos in range(self.motif_length):\n",
        "            nucleotide_counts = {'A': 0, 'T': 0, 'G': 0, 'C': 0}\n",
        "\n",
        "            for motif in motif_instances:\n",
        "                if pos < len(motif) and motif[pos] in nucleotide_counts:\n",
        "                    nucleotide_counts[motif[pos]] += 1\n",
        "\n",
        "            consensus += max(nucleotide_counts, key=nucleotide_counts.get)\n",
        "\n",
        "        return consensus\n",
        "\n",
        "    def mutate(self, mutation_rate: float):\n",
        "        \"\"\"Mutate individual by changing positions\"\"\"\n",
        "        for i in range(len(self.positions)):\n",
        "            if random.random() < mutation_rate:\n",
        "                seq_length = len(self.sequences[i])\n",
        "                if seq_length >= self.motif_length:\n",
        "                    max_pos = seq_length - self.motif_length\n",
        "                    self.positions[i] = random.randint(0, max_pos)\n",
        "\n",
        "    def copy(self):\n",
        "        \"\"\"Create a copy of this individual\"\"\"\n",
        "        new_individual = Individual(self.sequences, self.motif_length)\n",
        "        new_individual.positions = self.positions.copy()\n",
        "        new_individual.fitness = self.fitness\n",
        "        new_individual.motif = self.motif\n",
        "        new_individual.pwm = self.pwm.copy() if self.pwm is not None else None\n",
        "        return new_individual\n",
        "\n",
        "class GeneticAlgorithm(BaseMotifDiscovery):\n",
        "    \"\"\"Genetic Algorithm for motif discovery\"\"\"\n",
        "\n",
        "    def __init__(self, sequences: List[str], motif_length: int, config: OptimizationConfig = None):\n",
        "        super().__init__(sequences, motif_length, config)\n",
        "        self.population = []\n",
        "        self.best_individual = None\n",
        "        self.convergence_history = []\n",
        "\n",
        "    def discover_motifs(self) -> MotifResult:\n",
        "        \"\"\"Main GA motif discovery algorithm\"\"\"\n",
        "        start_time = time.time()\n",
        "        print(f\"\\n🧬 Starting Genetic Algorithm motif discovery...\")\n",
        "        print(f\"   Population size: {self.config.population_size}\")\n",
        "        print(f\"   Max generations: {self.config.max_generations}\")\n",
        "        print(f\"   Motif length: {self.motif_length}\")\n",
        "\n",
        "        try:\n",
        "            # Initialize population\n",
        "            self._initialize_population()\n",
        "\n",
        "            # Evolution loop\n",
        "            stagnation_count = 0\n",
        "            best_fitness = 0.0\n",
        "\n",
        "            for generation in range(self.config.max_generations):\n",
        "                # Evaluate fitness\n",
        "                self._evaluate_population()\n",
        "\n",
        "                # Track best individual\n",
        "                current_best = max(self.population, key=lambda x: x.fitness)\n",
        "                self.convergence_history.append(current_best.fitness)\n",
        "\n",
        "                # Check for improvement\n",
        "                if current_best.fitness > best_fitness + self.config.convergence_threshold:\n",
        "                    best_fitness = current_best.fitness\n",
        "                    self.best_individual = current_best.copy()\n",
        "                    stagnation_count = 0\n",
        "                else:\n",
        "                    stagnation_count += 1\n",
        "\n",
        "                # Progress update\n",
        "                if generation % 20 == 0:\n",
        "                    print(f\"   Generation {generation}: Best fitness = {best_fitness:.4f}\")\n",
        "\n",
        "                # Check convergence\n",
        "                if stagnation_count >= self.config.max_stagnation:\n",
        "                    print(f\"   Converged after {generation + 1} generations (stagnation)\")\n",
        "                    break\n",
        "\n",
        "                # Selection and reproduction\n",
        "                self._create_next_generation()\n",
        "\n",
        "            # Return best result\n",
        "            if self.best_individual:\n",
        "                runtime = time.time() - start_time\n",
        "                print(f\"✅ GA completed! Best motif: {self.best_individual.motif}\")\n",
        "                print(f\"   Final fitness: {self.best_individual.fitness:.4f}\")\n",
        "                print(f\"   Runtime: {runtime:.2f}s\")\n",
        "\n",
        "                return MotifResult(\n",
        "                    motif=self.best_individual.motif,\n",
        "                    score=self.best_individual.fitness,\n",
        "                    positions=self.best_individual.positions,\n",
        "                    pwm=self.best_individual.pwm,\n",
        "                    information_content=self.best_individual._calculate_information_content(self.best_individual.pwm),\n",
        "                    algorithm=\"Genetic_Algorithm\",\n",
        "                    runtime=runtime,\n",
        "                    convergence_history=self.convergence_history\n",
        "                )\n",
        "            else:\n",
        "                print(\"❌ GA failed to find valid motif\")\n",
        "                return self._create_dummy_result(\"Genetic_Algorithm\")\n",
        "\n",
        "        except Exception as e:\n",
        "            print(f\"❌ GA error: {e}\")\n",
        "            return self._create_dummy_result(\"Genetic_Algorithm\")\n",
        "\n",
        "    def _initialize_population(self):\n",
        "        \"\"\"Initialize random population\"\"\"\n",
        "        print(f\"   Initializing population of {self.config.population_size} individuals...\")\n",
        "        self.population = []\n",
        "\n",
        "        for _ in range(self.config.population_size):\n",
        "            individual = Individual(self.sequences, self.motif_length)\n",
        "            self.population.append(individual)\n",
        "\n",
        "    def _evaluate_population(self):\n",
        "        \"\"\"Evaluate fitness for all individuals\"\"\"\n",
        "        for individual in self.population:\n",
        "            individual.calculate_fitness()\n",
        "\n",
        "    def _create_next_generation(self):\n",
        "        \"\"\"Create next generation using selection, crossover, and mutation\"\"\"\n",
        "        # Sort by fitness\n",
        "        self.population.sort(key=lambda x: x.fitness, reverse=True)\n",
        "\n",
        "        new_population = []\n",
        "\n",
        "        # Elitism - keep best individuals\n",
        "        elite_size = min(self.config.elitism_size, len(self.population))\n",
        "        for i in range(elite_size):\n",
        "            new_population.append(self.population[i].copy())\n",
        "\n",
        "        # Generate offspring\n",
        "        while len(new_population) < self.config.population_size:\n",
        "            # Tournament selection\n",
        "            parent1 = self._tournament_selection()\n",
        "            parent2 = self._tournament_selection()\n",
        "\n",
        "            # Crossover\n",
        "            if random.random() < self.config.crossover_rate:\n",
        "                child1, child2 = self._crossover(parent1, parent2)\n",
        "            else:\n",
        "                child1, child2 = parent1.copy(), parent2.copy()\n",
        "\n",
        "            # Mutation\n",
        "            child1.mutate(self.config.mutation_rate)\n",
        "            child2.mutate(self.config.mutation_rate)\n",
        "\n",
        "            new_population.extend([child1, child2])\n",
        "\n",
        "        # Trim to exact population size\n",
        "        self.population = new_population[:self.config.population_size]\n",
        "\n",
        "    def _tournament_selection(self, tournament_size: int = 3) -> Individual:\n",
        "        \"\"\"Tournament selection\"\"\"\n",
        "        tournament = random.sample(self.population, min(tournament_size, len(self.population)))\n",
        "        return max(tournament, key=lambda x: x.fitness)\n",
        "\n",
        "    def _crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:\n",
        "        \"\"\"Single-point crossover\"\"\"\n",
        "        if len(parent1.positions) != len(parent2.positions):\n",
        "            return parent1.copy(), parent2.copy()\n",
        "\n",
        "        crossover_point = random.randint(1, len(parent1.positions) - 1)\n",
        "\n",
        "        child1 = Individual(self.sequences, self.motif_length)\n",
        "        child2 = Individual(self.sequences, self.motif_length)\n",
        "\n",
        "        child1.positions = parent1.positions[:crossover_point] + parent2.positions[crossover_point:]\n",
        "        child2.positions = parent2.positions[:crossover_point] + parent1.positions[crossover_point:]\n",
        "\n",
        "        return child1, child2\n",
        "\n",
        "print(\"✅ Genetic Algorithm implementation completed!\")"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/"
        },
        "id": "2whGUPkzT_Pe",
        "outputId": "2de4bf66-f064-4d96-e158-9117a0cd5cb8"
      },
      "execution_count": 4,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "✅ Genetic Algorithm implementation completed!\n"
          ]
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Particle Swarm Optimization Implementation\n",
        "class Particle:\n",
        "    \"\"\"Particle in PSO swarm representing motif positions\"\"\"\n",
        "\n",
        "    def __init__(self, sequences: List[str], motif_length: int):\n",
        "        self.sequences = sequences\n",
        "        self.motif_length = motif_length\n",
        "\n",
        "        # Position (motif start positions in each sequence)\n",
        "        self.position = self._initialize_position()\n",
        "        self.velocity = self._initialize_velocity()\n",
        "\n",
        "        # Personal best\n",
        "        self.best_position = self.position.copy()\n",
        "        self.best_fitness = 0.0\n",
        "\n",
        "        # Current fitness\n",
        "        self.fitness = 0.0\n",
        "        self.motif = \"\"\n",
        "        self.pwm = None\n",
        "\n",
        "    def _initialize_position(self) -> List[float]:\n",
        "        \"\"\"Initialize random positions (continuous values)\"\"\"\n",
        "        positions = []\n",
        "        for seq in self.sequences:\n",
        "            if len(seq) >= self.motif_length:\n",
        "                max_pos = len(seq) - self.motif_length\n",
        "                positions.append(random.uniform(0, max_pos))\n",
        "            else:\n",
        "                positions.append(0.0)\n",
        "        return positions\n",
        "\n",
        "    def _initialize_velocity(self) -> List[float]:\n",
        "        \"\"\"Initialize random velocities\"\"\"\n",
        "        velocities = []\n",
        "        for seq in self.sequences:\n",
        "            if len(seq) >= self.motif_length:\n",
        "                max_pos = len(seq) - self.motif_length\n",
        "                # Small initial velocities\n",
        "                velocities.append(random.uniform(-max_pos * 0.1, max_pos * 0.1))\n",
        "            else:\n",
        "                velocities.append(0.0)\n",
        "        return velocities\n",
        "\n",
        "    def get_discrete_positions(self) -> List[int]:\n",
        "        \"\"\"Convert continuous positions to discrete positions\"\"\"\n",
        "        discrete_positions = []\n",
        "        for i, pos in enumerate(self.position):\n",
        "            seq_length = len(self.sequences[i])\n",
        "            if seq_length >= self.motif_length:\n",
        "                max_pos = seq_length - self.motif_length\n",
        "                discrete_pos = max(0, min(int(round(pos)), max_pos))\n",
        "                discrete_positions.append(discrete_pos)\n",
        "            else:\n",
        "                discrete_positions.append(0)\n",
        "        return discrete_positions\n",
        "\n",
        "    def calculate_fitness(self) -> float:\n",
        "        \"\"\"Calculate fitness based on motif quality\"\"\"\n",
        "        discrete_positions = self.get_discrete_positions()\n",
        "\n",
        "        # Extract motif instances\n",
        "        motif_instances = []\n",
        "        for i, pos in enumerate(discrete_positions):\n",
        "            if pos + self.motif_length <= len(self.sequences[i]):\n",
        "                instance = self.sequences[i][pos:pos + self.motif_length]\n",
        "                motif_instances.append(instance)\n",
        "\n",
        "        if not motif_instances:\n",
        "            self.fitness = 0.0\n",
        "            return self.fitness\n",
        "\n",
        "        # Calculate PWM and information content\n",
        "        self.pwm = self._calculate_pwm(motif_instances)\n",
        "        ic = self._calculate_information_content(self.pwm)\n",
        "\n",
        "        # Calculate conservation score\n",
        "        conservation = self._calculate_conservation_score(motif_instances)\n",
        "\n",
        "        # Get consensus motif\n",
        "        self.motif = self._get_consensus(motif_instances)\n",
        "\n",
        "        # Combined fitness\n",
        "        length_penalty = len(motif_instances) / len(self.sequences)\n",
        "        self.fitness = ic * conservation * length_penalty\n",
        "\n",
        "        # Update personal best\n",
        "        if self.fitness > self.best_fitness:\n",
        "            self.best_fitness = self.fitness\n",
        "            self.best_position = self.position.copy()\n",
        "\n",
        "        return self.fitness\n",
        "\n",
        "    def update_velocity(self, global_best_position: List[float], inertia: float,\n",
        "                       cognitive_coef: float, social_coef: float):\n",
        "        \"\"\"Update particle velocity\"\"\"\n",
        "        for i in range(len(self.velocity)):\n",
        "            # Inertia component\n",
        "            inertia_component = inertia * self.velocity[i]\n",
        "\n",
        "            # Cognitive component (personal best)\n",
        "            cognitive_component = cognitive_coef * random.random() * (self.best_position[i] - self.position[i])\n",
        "\n",
        "            # Social component (global best)\n",
        "            social_component = social_coef * random.random() * (global_best_position[i] - self.position[i])\n",
        "\n",
        "            # Update velocity\n",
        "            self.velocity[i] = inertia_component + cognitive_component + social_component\n",
        "\n",
        "            # Velocity clamping\n",
        "            seq_length = len(self.sequences[i])\n",
        "            if seq_length >= self.motif_length:\n",
        "                max_velocity = (seq_length - self.motif_length) * 0.2  # 20% of range\n",
        "                self.velocity[i] = max(-max_velocity, min(max_velocity, self.velocity[i]))\n",
        "\n",
        "    def update_position(self):\n",
        "        \"\"\"Update particle position\"\"\"\n",
        "        for i in range(len(self.position)):\n",
        "            self.position[i] += self.velocity[i]\n",
        "\n",
        "            # Position bounds\n",
        "            seq_length = len(self.sequences[i])\n",
        "            if seq_length >= self.motif_length:\n",
        "                max_pos = seq_length - self.motif_length\n",
        "                self.position[i] = max(0.0, min(float(max_pos), self.position[i]))\n",
        "\n",
        "    def _calculate_pwm(self, motif_instances: List[str]) -> np.ndarray:\n",
        "        \"\"\"Calculate Position Weight Matrix\"\"\"\n",
        "        if not motif_instances:\n",
        "            return np.ones((4, self.motif_length)) * 0.25\n",
        "\n",
        "        pwm = np.zeros((4, self.motif_length))\n",
        "        nucleotide_to_index = {'A': 0, 'T': 1, 'G': 2, 'C': 3}\n",
        "        pseudocount = 0.001\n",
        "\n",
        "        for pos in range(self.motif_length):\n",
        "            for motif in motif_instances:\n",
        "                if pos < len(motif) and motif[pos] in nucleotide_to_index:\n",
        "                    pwm[nucleotide_to_index[motif[pos]], pos] += 1\n",
        "\n",
        "            pwm[:, pos] += pseudocount\n",
        "            pwm[:, pos] /= np.sum(pwm[:, pos])\n",
        "\n",
        "        return pwm\n",
        "\n",
        "    def _calculate_information_content(self, pwm: np.ndarray) -> float:\n",
        "        \"\"\"Calculate information content\"\"\"\n",
        "        ic_total = 0.0\n",
        "        for pos in range(pwm.shape[1]):\n",
        "            ic_pos = 0.0\n",
        "            for nuc_prob in pwm[:, pos]:\n",
        "                if nuc_prob > 0:\n",
        "                    ic_pos += nuc_prob * np.log2(nuc_prob / 0.25)\n",
        "            ic_total += ic_pos\n",
        "        return ic_total\n",
        "\n",
        "    def _calculate_conservation_score(self, motif_instances: List[str]) -> float:\n",
        "        \"\"\"Calculate conservation score\"\"\"\n",
        "        if len(motif_instances) < 2:\n",
        "            return 0.0\n",
        "\n",
        "        total_similarity = 0.0\n",
        "        comparisons = 0\n",
        "\n",
        "        for i in range(len(motif_instances)):\n",
        "            for j in range(i + 1, len(motif_instances)):\n",
        "                seq1, seq2 = motif_instances[i], motif_instances[j]\n",
        "                if len(seq1) == len(seq2):\n",
        "                    similarity = sum(1 for a, b in zip(seq1, seq2) if a == b) / len(seq1)\n",
        "                    total_similarity += similarity\n",
        "                    comparisons += 1\n",
        "\n",
        "        return total_similarity / comparisons if comparisons > 0 else 0.0\n",
        "\n",
        "    def _get_consensus(self, motif_instances: List[str]) -> str:\n",
        "        \"\"\"Get consensus sequence\"\"\"\n",
        "        if not motif_instances:\n",
        "            return \"A\" * self.motif_length\n",
        "\n",
        "        consensus = \"\"\n",
        "        for pos in range(self.motif_length):\n",
        "            nucleotide_counts = {'A': 0, 'T': 0, 'G': 0, 'C': 0}\n",
        "\n",
        "            for motif in motif_instances:\n",
        "                if pos < len(motif) and motif[pos] in nucleotide_counts:\n",
        "                    nucleotide_counts[motif[pos]] += 1\n",
        "\n",
        "            consensus += max(nucleotide_counts, key=nucleotide_counts.get)\n",
        "\n",
        "        return consensus\n",
        "\n",
        "class ParticleSwarmOptimization(BaseMotifDiscovery):\n",
        "    \"\"\"Particle Swarm Optimization for motif discovery\"\"\"\n",
        "\n",
        "    def __init__(self, sequences: List[str], motif_length: int, config: OptimizationConfig = None):\n",
        "        super().__init__(sequences, motif_length, config)\n",
        "        self.swarm = []\n",
        "        self.global_best_particle = None\n",
        "        self.global_best_position = []\n",
        "        self.global_best_fitness = 0.0\n",
        "        self.convergence_history = []\n",
        "\n",
        "    def discover_motifs(self) -> MotifResult:\n",
        "        \"\"\"Main PSO motif discovery algorithm\"\"\"\n",
        "        start_time = time.time()\n",
        "        print(f\"\\n🐦 Starting Particle Swarm Optimization motif discovery...\")\n",
        "        print(f\"   Swarm size: {self.config.population_size}\")\n",
        "        print(f\"   Max iterations: {self.config.max_generations}\")\n",
        "        print(f\"   Motif length: {self.motif_length}\")\n",
        "\n",
        "        try:\n",
        "            # Initialize swarm\n",
        "            self._initialize_swarm()\n",
        "\n",
        "            # PSO loop\n",
        "            stagnation_count = 0\n",
        "\n",
        "            for iteration in range(self.config.max_generations):\n",
        "                # Evaluate fitness\n",
        "                self._evaluate_swarm()\n",
        "\n",
        "                # Track convergence\n",
        "                self.convergence_history.append(self.global_best_fitness)\n",
        "\n",
        "                # Check for stagnation\n",
        "                if len(self.convergence_history) > 1:\n",
        "                    improvement = abs(self.convergence_history[-1] - self.convergence_history[-2])\n",
        "                    if improvement < self.config.convergence_threshold:\n",
        "                        stagnation_count += 1\n",
        "                    else:\n",
        "                        stagnation_count = 0\n",
        "\n",
        "                # Progress update\n",
        "                if iteration % 20 == 0:\n",
        "                    print(f\"   Iteration {iteration}: Best fitness = {self.global_best_fitness:.4f}\")\n",
        "\n",
        "                # Check convergence\n",
        "                if stagnation_count >= self.config.max_stagnation:\n",
        "                    print(f\"   Converged after {iteration + 1} iterations (stagnation)\")\n",
        "                    break\n",
        "\n",
        "                # Update swarm\n",
        "                self._update_swarm()\n",
        "\n",
        "            # Return best result\n",
        "            if self.global_best_particle:\n",
        "                runtime = time.time() - start_time\n",
        "                print(f\"✅ PSO completed! Best motif: {self.global_best_particle.motif}\")\n",
        "                print(f\"   Final fitness: {self.global_best_fitness:.4f}\")\n",
        "                print(f\"   Runtime: {runtime:.2f}s\")\n",
        "\n",
        "                return MotifResult(\n",
        "                    motif=self.global_best_particle.motif,\n",
        "                    score=self.global_best_fitness,\n",
        "                    positions=self.global_best_particle.get_discrete_positions(),\n",
        "                    pwm=self.global_best_particle.pwm,\n",
        "                    information_content=self.global_best_particle._calculate_information_content(self.global_best_particle.pwm),\n",
        "                    algorithm=\"Particle_Swarm_Optimization\",\n",
        "                    runtime=runtime,\n",
        "                    convergence_history=self.convergence_history\n",
        "                )\n",
        "            else:\n",
        "                print(\"❌ PSO failed to find valid motif\")\n",
        "                return self._create_dummy_result(\"Particle_Swarm_Optimization\")\n",
        "\n",
        "        except Exception as e:\n",
        "            print(f\"❌ PSO error: {e}\")\n",
        "            return self._create_dummy_result(\"Particle_Swarm_Optimization\")\n",
        "\n",
        "    def _initialize_swarm(self):\n",
        "        \"\"\"Initialize random swarm\"\"\"\n",
        "        print(f\"   Initializing swarm of {self.config.population_size} particles...\")\n",
        "        self.swarm = []\n",
        "\n",
        "        for _ in range(self.config.population_size):\n",
        "            particle = Particle(self.sequences, self.motif_length)\n",
        "            self.swarm.append(particle)\n",
        "\n",
        "    def _evaluate_swarm(self):\n",
        "        \"\"\"Evaluate fitness for all particles and update global best\"\"\"\n",
        "        for particle in self.swarm:\n",
        "            particle.calculate_fitness()\n",
        "\n",
        "            # Update global best\n",
        "            if particle.fitness > self.global_best_fitness:\n",
        "                self.global_best_fitness = particle.fitness\n",
        "                self.global_best_particle = particle\n",
        "                self.global_best_position = particle.position.copy()\n",
        "\n",
        "    def _update_swarm(self):\n",
        "        \"\"\"Update velocities and positions of all particles\"\"\"\n",
        "        for particle in self.swarm:\n",
        "            # Update velocity\n",
        "            particle.update_velocity(\n",
        "                self.global_best_position,\n",
        "                self.config.inertia_weight,\n",
        "                self.config.cognitive_coef,\n",
        "                self.config.social_coef\n",
        "            )\n",
        "\n",
        "            # Update position\n",
        "            particle.update_position()\n",
        "\n",
        "print(\"✅ Particle Swarm Optimization implementation completed!\")"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/"
        },
        "id": "MAGLWGqUU5u3",
        "outputId": "70a12363-3ca9-4cb0-d2cc-341547bbce0e"
      },
      "execution_count": 5,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "✅ Particle Swarm Optimization implementation completed!\n"
          ]
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Hybrid GA+PSO Implementation\n",
        "class HybridGAPSO(BaseMotifDiscovery):\n",
        "    \"\"\"Hybrid Genetic Algorithm + Particle Swarm Optimization for motif discovery\"\"\"\n",
        "\n",
        "    def __init__(self, sequences: List[str], motif_length: int, config: OptimizationConfig = None):\n",
        "        super().__init__(sequences, motif_length, config)\n",
        "\n",
        "        # Split population between GA and PSO\n",
        "        self.ga_size = self.config.population_size // 2\n",
        "        self.pso_size = self.config.population_size - self.ga_size\n",
        "\n",
        "        # GA components\n",
        "        self.ga_population = []\n",
        "        self.best_ga_individual = None\n",
        "\n",
        "        # PSO components\n",
        "        self.pso_swarm = []\n",
        "        self.global_best_particle = None\n",
        "        self.global_best_position = []\n",
        "        self.global_best_fitness = 0.0\n",
        "\n",
        "        # Overall tracking\n",
        "        self.convergence_history = []\n",
        "        self.best_overall = None\n",
        "        self.best_overall_fitness = 0.0\n",
        "\n",
        "    def discover_motifs(self) -> MotifResult:\n",
        "        \"\"\"Main Hybrid GA+PSO motif discovery algorithm\"\"\"\n",
        "        start_time = time.time()\n",
        "        print(f\"\\n🔥 Starting Hybrid GA+PSO motif discovery...\")\n",
        "        print(f\"   GA population: {self.ga_size}\")\n",
        "        print(f\"   PSO swarm: {self.pso_size}\")\n",
        "        print(f\"   Max generations: {self.config.max_generations}\")\n",
        "        print(f\"   Motif length: {self.motif_length}\")\n",
        "\n",
        "        try:\n",
        "            # Initialize both populations\n",
        "            self._initialize_populations()\n",
        "\n",
        "            # Hybrid evolution loop\n",
        "            stagnation_count = 0\n",
        "            migration_interval = 10  # Exchange information every 10 generations\n",
        "\n",
        "            for generation in range(self.config.max_generations):\n",
        "                # Run GA step\n",
        "                self._ga_step()\n",
        "\n",
        "                # Run PSO step\n",
        "                self._pso_step()\n",
        "\n",
        "                # Information exchange (migration)\n",
        "                if generation % migration_interval == 0:\n",
        "                    self._information_exchange()\n",
        "\n",
        "                # Track overall best\n",
        "                current_best_fitness = max(self.best_overall_fitness,\n",
        "                                         self.best_ga_individual.fitness if self.best_ga_individual else 0,\n",
        "                                         self.global_best_fitness)\n",
        "\n",
        "                self.convergence_history.append(current_best_fitness)\n",
        "\n",
        "                # Check for improvement\n",
        "                if current_best_fitness > self.best_overall_fitness + self.config.convergence_threshold:\n",
        "                    self.best_overall_fitness = current_best_fitness\n",
        "\n",
        "                    # Determine which algorithm found the best solution\n",
        "                    if self.best_ga_individual and self.best_ga_individual.fitness >= current_best_fitness:\n",
        "                        self.best_overall = self.best_ga_individual\n",
        "                    elif self.global_best_particle and self.global_best_fitness >= current_best_fitness:\n",
        "                        self.best_overall = self.global_best_particle\n",
        "\n",
        "                    stagnation_count = 0\n",
        "                else:\n",
        "                    stagnation_count += 1\n",
        "\n",
        "                # Progress update\n",
        "                if generation % 20 == 0:\n",
        "                    print(f\"   Generation {generation}:\")\n",
        "                    print(f\"     GA best: {self.best_ga_individual.fitness:.4f}\" if self.best_ga_individual else \"     GA best: 0.0000\")\n",
        "                    print(f\"     PSO best: {self.global_best_fitness:.4f}\")\n",
        "                    print(f\"     Overall best: {self.best_overall_fitness:.4f}\")\n",
        "\n",
        "                # Check convergence\n",
        "                if stagnation_count >= self.config.max_stagnation:\n",
        "                    print(f\"   Converged after {generation + 1} generations (stagnation)\")\n",
        "                    break\n",
        "\n",
        "            # Return best result\n",
        "            if self.best_overall:\n",
        "                runtime = time.time() - start_time\n",
        "\n",
        "                # Create result based on best solution type\n",
        "                if isinstance(self.best_overall, Individual):\n",
        "                    # GA solution\n",
        "                    motif = self.best_overall.motif\n",
        "                    positions = self.best_overall.positions\n",
        "                    pwm = self.best_overall.pwm\n",
        "                    score = self.best_overall.fitness\n",
        "                elif isinstance(self.best_overall, Particle):\n",
        "                    # PSO solution\n",
        "                    motif = self.best_overall.motif\n",
        "                    positions = self.best_overall.get_discrete_positions()\n",
        "                    pwm = self.best_overall.pwm\n",
        "                    score = self.best_overall.fitness\n",
        "                else:\n",
        "                    raise ValueError(\"Unknown best solution type\")\n",
        "\n",
        "                print(f\"✅ Hybrid GA+PSO completed! Best motif: {motif}\")\n",
        "                print(f\"   Final fitness: {score:.4f}\")\n",
        "                print(f\"   Runtime: {runtime:.2f}s\")\n",
        "\n",
        "                ic = self._calculate_information_content_from_pwm(pwm) if pwm is not None else 0.0\n",
        "\n",
        "                return MotifResult(\n",
        "                    motif=motif,\n",
        "                    score=score,\n",
        "                    positions=positions,\n",
        "                    pwm=pwm,\n",
        "                    information_content=ic,\n",
        "                    algorithm=\"Hybrid_GA_PSO\",\n",
        "                    runtime=runtime,\n",
        "                    convergence_history=self.convergence_history\n",
        "                )\n",
        "            else:\n",
        "                print(\"❌ Hybrid GA+PSO failed to find valid motif\")\n",
        "                return self._create_dummy_result(\"Hybrid_GA_PSO\")\n",
        "\n",
        "        except Exception as e:\n",
        "            print(f\"❌ Hybrid GA+PSO error: {e}\")\n",
        "            return self._create_dummy_result(\"Hybrid_GA_PSO\")\n",
        "\n",
        "    def _initialize_populations(self):\n",
        "        \"\"\"Initialize both GA and PSO populations\"\"\"\n",
        "        print(f\"   Initializing GA population ({self.ga_size}) and PSO swarm ({self.pso_size})...\")\n",
        "\n",
        "        # Initialize GA population\n",
        "        self.ga_population = []\n",
        "        for _ in range(self.ga_size):\n",
        "            individual = Individual(self.sequences, self.motif_length)\n",
        "            self.ga_population.append(individual)\n",
        "\n",
        "        # Initialize PSO swarm\n",
        "        self.pso_swarm = []\n",
        "        for _ in range(self.pso_size):\n",
        "            particle = Particle(self.sequences, self.motif_length)\n",
        "            self.pso_swarm.append(particle)\n",
        "\n",
        "    def _ga_step(self):\n",
        "        \"\"\"Perform one GA generation step\"\"\"\n",
        "        # Evaluate GA population\n",
        "        for individual in self.ga_population:\n",
        "            individual.calculate_fitness()\n",
        "\n",
        "        # Update best GA individual\n",
        "        current_best = max(self.ga_population, key=lambda x: x.fitness)\n",
        "        if self.best_ga_individual is None or current_best.fitness > self.best_ga_individual.fitness:\n",
        "            self.best_ga_individual = current_best.copy()\n",
        "\n",
        "        # Create next generation\n",
        "        self.ga_population.sort(key=lambda x: x.fitness, reverse=True)\n",
        "        new_population = []\n",
        "\n",
        "        # Elitism\n",
        "        elite_size = min(self.config.elitism_size, len(self.ga_population))\n",
        "        for i in range(elite_size):\n",
        "            new_population.append(self.ga_population[i].copy())\n",
        "\n",
        "        # Generate offspring\n",
        "        while len(new_population) < self.ga_size:\n",
        "            parent1 = self._tournament_selection(self.ga_population)\n",
        "            parent2 = self._tournament_selection(self.ga_population)\n",
        "\n",
        "            if random.random() < self.config.crossover_rate:\n",
        "                child1, child2 = self._crossover(parent1, parent2)\n",
        "            else:\n",
        "                child1, child2 = parent1.copy(), parent2.copy()\n",
        "\n",
        "            child1.mutate(self.config.mutation_rate)\n",
        "            child2.mutate(self.config.mutation_rate)\n",
        "\n",
        "            new_population.extend([child1, child2])\n",
        "\n",
        "        self.ga_population = new_population[:self.ga_size]\n",
        "\n",
        "    def _pso_step(self):\n",
        "        \"\"\"Perform one PSO iteration step\"\"\"\n",
        "        # Evaluate PSO swarm\n",
        "        for particle in self.pso_swarm:\n",
        "            particle.calculate_fitness()\n",
        "\n",
        "            # Update global best\n",
        "            if particle.fitness > self.global_best_fitness:\n",
        "                self.global_best_fitness = particle.fitness\n",
        "                self.global_best_particle = particle\n",
        "                self.global_best_position = particle.position.copy()\n",
        "\n",
        "        # Update swarm\n",
        "        for particle in self.pso_swarm:\n",
        "            particle.update_velocity(\n",
        "                self.global_best_position,\n",
        "                self.config.inertia_weight,\n",
        "                self.config.cognitive_coef,\n",
        "                self.config.social_coef\n",
        "            )\n",
        "            particle.update_position()\n",
        "\n",
        "    def _information_exchange(self):\n",
        "        \"\"\"Exchange information between GA and PSO populations\"\"\"\n",
        "        if not self.ga_population or not self.pso_swarm:\n",
        "            return\n",
        "\n",
        "        # Convert best GA individual to PSO particle\n",
        "        if self.best_ga_individual:\n",
        "            worst_particle_idx = min(range(len(self.pso_swarm)),\n",
        "                                   key=lambda i: self.pso_swarm[i].fitness)\n",
        "\n",
        "            # Replace worst PSO particle with GA-inspired particle\n",
        "            new_particle = Particle(self.sequences, self.motif_length)\n",
        "            new_particle.position = [float(pos) for pos in self.best_ga_individual.positions]\n",
        "            new_particle.velocity = [0.0] * len(new_particle.position)  # Start with zero velocity\n",
        "            self.pso_swarm[worst_particle_idx] = new_particle\n",
        "\n",
        "        # Convert best PSO particle to GA individual\n",
        "        if self.global_best_particle:\n",
        "            worst_individual_idx = min(range(len(self.ga_population)),\n",
        "                                     key=lambda i: self.ga_population[i].fitness)\n",
        "\n",
        "            # Replace worst GA individual with PSO-inspired individual\n",
        "            new_individual = Individual(self.sequences, self.motif_length)\n",
        "            new_individual.positions = self.global_best_particle.get_discrete_positions()\n",
        "            self.ga_population[worst_individual_idx] = new_individual\n",
        "\n",
        "    def _tournament_selection(self, population: List[Individual], tournament_size: int = 3) -> Individual:\n",
        "        \"\"\"Tournament selection for GA\"\"\"\n",
        "        tournament = random.sample(population, min(tournament_size, len(population)))\n",
        "        return max(tournament, key=lambda x: x.fitness)\n",
        "\n",
        "    def _crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:\n",
        "        \"\"\"Single-point crossover for GA\"\"\"\n",
        "        if len(parent1.positions) != len(parent2.positions):\n",
        "            return parent1.copy(), parent2.copy()\n",
        "\n",
        "        crossover_point = random.randint(1, len(parent1.positions) - 1)\n",
        "\n",
        "        child1 = Individual(self.sequences, self.motif_length)\n",
        "        child2 = Individual(self.sequences, self.motif_length)\n",
        "\n",
        "        child1.positions = parent1.positions[:crossover_point] + parent2.positions[crossover_point:]\n",
        "        child2.positions = parent2.positions[:crossover_point] + parent1.positions[crossover_point:]\n",
        "\n",
        "        return child1, child2\n",
        "\n",
        "    def _calculate_information_content_from_pwm(self, pwm: np.ndarray) -> float:\n",
        "        \"\"\"Calculate information content from PWM\"\"\"\n",
        "        if pwm is None:\n",
        "            return 0.0\n",
        "\n",
        "        ic_total = 0.0\n",
        "        for pos in range(pwm.shape[1]):\n",
        "            ic_pos = 0.0\n",
        "            for nuc_prob in pwm[:, pos]:\n",
        "                if nuc_prob > 0:\n",
        "                    ic_pos += nuc_prob * np.log2(nuc_prob / 0.25)\n",
        "            ic_total += ic_pos\n",
        "        return ic_total\n",
        "\n",
        "print(\"✅ Hybrid GA+PSO implementation completed!\")# Mount Google Drive and Setup"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/"
        },
        "id": "6Ru_52aXVSBj",
        "outputId": "caa2cd0c-c106-43fc-cc70-404038aef4f5"
      },
      "execution_count": 6,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "✅ Hybrid GA+PSO implementation completed!\n"
          ]
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Run Genetic Algorithm\n",
        "print(\"\\n\" + \"=\"*60)\n",
        "print(\"🧬 GENETIC ALGORITHM MOTIF DISCOVERY\")\n",
        "print(\"=\"*60)\n",
        "\n",
        "ga_algorithm = GeneticAlgorithm(analysis_sequences, motif_length, config)\n",
        "ga_result = ga_algorithm.discover_motifs()\n",
        "\n",
        "print(f\"\\n📊 GA Results Summary:\")\n",
        "print(f\"   Best motif: {ga_result.motif}\")\n",
        "print(f\"   Score: {ga_result.score:.4f}\")\n",
        "print(f\"   Information content: {ga_result.information_content:.4f}\")\n",
        "print(f\"   Runtime: {ga_result.runtime:.2f} seconds\")\n",
        "print(f\"   Algorithm: {ga_result.algorithm}\")\n",
        "\n",
        "# Visualize GA convergence\n",
        "if ga_result.convergence_history:\n",
        "    plt.figure(figsize=(10, 6))\n",
        "    plt.plot(ga_result.convergence_history, 'b-', linewidth=2, marker='o', markersize=4)\n",
        "    plt.title('Genetic Algorithm Convergence', fontsize=14, fontweight='bold')\n",
        "    plt.xlabel('Generation')\n",
        "    plt.ylabel('Fitness Score')\n",
        "    plt.grid(True, alpha=0.3)\n",
        "    plt.tight_layout()\n",
        "    plt.show()\n",
        "\n",
        "\n"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/",
          "height": 1000
        },
        "id": "tIh7C5XoTWoS",
        "outputId": "6471aba8-3db5-44ca-e1d9-b1cbde742916"
      },
      "execution_count": 7,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "\n",
            "============================================================\n",
            "🧬 GENETIC ALGORITHM MOTIF DISCOVERY\n",
            "============================================================\n",
            "\n",
            "🧬 Starting Genetic Algorithm motif discovery...\n",
            "   Population size: 30\n",
            "   Max generations: 50\n",
            "   Motif length: 8\n",
            "   Initializing population of 30 individuals...\n",
            "   Generation 0: Best fitness = 0.9853\n",
            "   Generation 20: Best fitness = 3.2618\n",
            "   Generation 40: Best fitness = 4.1607\n",
            "✅ GA completed! Best motif: AAAAAAAA\n",
            "   Final fitness: 5.7374\n",
            "   Runtime: 0.82s\n",
            "\n",
            "📊 GA Results Summary:\n",
            "   Best motif: AAAAAAAA\n",
            "   Score: 5.7374\n",
            "   Information content: 8.9281\n",
            "   Runtime: 0.82 seconds\n",
            "   Algorithm: Genetic_Algorithm\n"
          ]
        },
        {
          "output_type": "display_data",
          "data": {
            "text/plain": [
              "<Figure size 1000x600 with 1 Axes>"
            ],
            "image/png": "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***************************************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\n"
          },
          "metadata": {}
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Run Particle Swarm Optimization\n",
        "print(\"\\n\" + \"=\"*60)\n",
        "print(\"🐦 PARTICLE SWARM OPTIMIZATION MOTIF DISCOVERY\")\n",
        "print(\"=\"*60)\n",
        "\n",
        "pso_algorithm = ParticleSwarmOptimization(analysis_sequences, motif_length, config)\n",
        "pso_result = pso_algorithm.discover_motifs()\n",
        "\n",
        "print(f\"\\n📊 PSO Results Summary:\")\n",
        "print(f\"   Best motif: {pso_result.motif}\")\n",
        "print(f\"   Score: {pso_result.score:.4f}\")\n",
        "print(f\"   Information content: {pso_result.information_content:.4f}\")\n",
        "print(f\"   Runtime: {pso_result.runtime:.2f} seconds\")\n",
        "print(f\"   Algorithm: {pso_result.algorithm}\")\n",
        "\n",
        "# Visualize PSO convergence\n",
        "if pso_result.convergence_history:\n",
        "    plt.figure(figsize=(10, 6))\n",
        "    plt.plot(pso_result.convergence_history, 'r-', linewidth=2, marker='s', markersize=4)\n",
        "    plt.title('Particle Swarm Optimization Convergence', fontsize=14, fontweight='bold')\n",
        "    plt.xlabel('Iteration')\n",
        "    plt.ylabel('Fitness Score')\n",
        "    plt.grid(True, alpha=0.3)\n",
        "    plt.tight_layout()\n",
        "    plt.show()"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/",
          "height": 1000
        },
        "id": "-6O3uzLDUVcm",
        "outputId": "edb4e59c-034b-48d2-d3f8-1e96b16796dd"
      },
      "execution_count": 8,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "\n",
            "============================================================\n",
            "🐦 PARTICLE SWARM OPTIMIZATION MOTIF DISCOVERY\n",
            "============================================================\n",
            "\n",
            "🐦 Starting Particle Swarm Optimization motif discovery...\n",
            "   Swarm size: 30\n",
            "   Max iterations: 50\n",
            "   Motif length: 8\n",
            "   Initializing swarm of 30 particles...\n",
            "   Iteration 0: Best fitness = 1.1443\n",
            "   Iteration 20: Best fitness = 1.3301\n",
            "   Converged after 31 iterations (stagnation)\n",
            "✅ PSO completed! Best motif: ACATGTCA\n",
            "   Final fitness: 1.3301\n",
            "   Runtime: 0.29s\n",
            "\n",
            "📊 PSO Results Summary:\n",
            "   Best motif: ACATGTCA\n",
            "   Score: 1.3301\n",
            "   Information content: 1.0695\n",
            "   Runtime: 0.29 seconds\n",
            "   Algorithm: Particle_Swarm_Optimization\n"
          ]
        },
        {
          "output_type": "display_data",
          "data": {
            "text/plain": [
              "<Figure size 1000x600 with 1 Axes>"
            ],
            "image/png": "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\n"
          },
          "metadata": {}
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Run Hybrid GA+PSO\n",
        "print(\"\\n\" + \"=\"*60)\n",
        "print(\"🔥 HYBRID GA+PSO MOTIF DISCOVERY\")\n",
        "print(\"=\"*60)\n",
        "\n",
        "hybrid_algorithm = HybridGAPSO(analysis_sequences, motif_length, config)\n",
        "hybrid_result = hybrid_algorithm.discover_motifs()\n",
        "\n",
        "print(f\"\\n📊 Hybrid GA+PSO Results Summary:\")\n",
        "print(f\"   Best motif: {hybrid_result.motif}\")\n",
        "print(f\"   Score: {hybrid_result.score:.4f}\")\n",
        "print(f\"   Information content: {hybrid_result.information_content:.4f}\")\n",
        "print(f\"   Runtime: {hybrid_result.runtime:.2f} seconds\")\n",
        "print(f\"   Algorithm: {hybrid_result.algorithm}\")\n",
        "\n",
        "# Visualize Hybrid convergence\n",
        "if hybrid_result.convergence_history:\n",
        "    plt.figure(figsize=(10, 6))\n",
        "    plt.plot(hybrid_result.convergence_history, 'g-', linewidth=2, marker='^', markersize=4)\n",
        "    plt.title('Hybrid GA+PSO Convergence', fontsize=14, fontweight='bold')\n",
        "    plt.xlabel('Generation')\n",
        "    plt.ylabel('Fitness Score')\n",
        "    plt.grid(True, alpha=0.3)\n",
        "    plt.tight_layout()\n",
        "    plt.show()\n"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/",
          "height": 1000
        },
        "id": "5p2bu_MyTWlA",
        "outputId": "7d5d39b0-26c3-4de1-a183-8b12311528cf"
      },
      "execution_count": 9,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "\n",
            "============================================================\n",
            "🔥 HYBRID GA+PSO MOTIF DISCOVERY\n",
            "============================================================\n",
            "\n",
            "🔥 Starting Hybrid GA+PSO motif discovery...\n",
            "   GA population: 15\n",
            "   PSO swarm: 15\n",
            "   Max generations: 50\n",
            "   Motif length: 8\n",
            "   Initializing GA population (15) and PSO swarm (15)...\n",
            "   Generation 0:\n",
            "     GA best: 0.7795\n",
            "     PSO best: 1.0574\n",
            "     Overall best: 1.0574\n",
            "   Generation 20:\n",
            "     GA best: 2.4756\n",
            "     PSO best: 1.6905\n",
            "     Overall best: 2.4756\n",
            "   Generation 40:\n",
            "     GA best: 3.6065\n",
            "     PSO best: 3.2381\n",
            "     Overall best: 3.6065\n",
            "✅ Hybrid GA+PSO completed! Best motif: AAAAAAAA\n",
            "   Final fitness: 4.2190\n",
            "   Runtime: 0.48s\n",
            "\n",
            "📊 Hybrid GA+PSO Results Summary:\n",
            "   Best motif: AAAAAAAA\n",
            "   Score: 4.2190\n",
            "   Information content: 7.5650\n",
            "   Runtime: 0.48 seconds\n",
            "   Algorithm: Hybrid_GA_PSO\n"
          ]
        },
        {
          "output_type": "display_data",
          "data": {
            "text/plain": [
              "<Figure size 1000x600 with 1 Axes>"
            ],
            "image/png": "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\n"
          },
          "metadata": {}
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        " #Compare all algorithms\n",
        "print(\"\\n\" + \"=\"*60)\n",
        "print(\"📈 ALGORITHM COMPARISON\")\n",
        "print(\"=\"*60)\n",
        "\n",
        "results = [ga_result, pso_result, hybrid_result]\n",
        "algorithms = ['GA', 'PSO', 'Hybrid GA+PSO']\n",
        "colors = ['blue', 'red', 'green']\n",
        "\n",
        "# Performance comparison\n",
        "print(\"🏆 Performance Comparison:\")\n",
        "print(f\"{'Algorithm':<15} {'Motif':<10} {'Score':<8} {'Info Content':<12} {'Runtime (s)':<12}\")\n",
        "print(\"-\" * 65)\n",
        "for i, result in enumerate(results):\n",
        "    print(f\"{algorithms[i]:<15} {result.motif:<10} {result.score:<8.4f} {result.information_content:<12.4f} {result.runtime:<12.2f}\")\n",
        "\n",
        "# Visualize comparison\n",
        "fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))\n",
        "\n",
        "# Score comparison\n",
        "scores = [r.score for r in results]\n",
        "ax1.bar(algorithms, scores, color=colors, alpha=0.7, edgecolor='black')\n",
        "ax1.set_title('Motif Score Comparison')\n",
        "ax1.set_ylabel('Fitness Score')\n",
        "ax1.grid(True, alpha=0.3)\n",
        "\n",
        "# Information content comparison\n",
        "ic_scores = [r.information_content for r in results]\n",
        "ax2.bar(algorithms, ic_scores, color=colors, alpha=0.7, edgecolor='black')\n",
        "ax2.set_title('Information Content Comparison')\n",
        "ax2.set_ylabel('Information Content (bits)')\n",
        "ax2.grid(True, alpha=0.3)\n",
        "\n",
        "# Runtime comparison\n",
        "runtimes = [r.runtime for r in results]\n",
        "ax3.bar(algorithms, runtimes, color=colors, alpha=0.7, edgecolor='black')\n",
        "ax3.set_title('Runtime Comparison')\n",
        "ax3.set_ylabel('Runtime (seconds)')\n",
        "ax3.grid(True, alpha=0.3)\n",
        "\n",
        "# Convergence comparison\n",
        "ax4.set_title('Convergence Comparison')\n",
        "for i, result in enumerate(results):\n",
        "    if result.convergence_history:\n",
        "        ax4.plot(result.convergence_history, color=colors[i], linewidth=2,\n",
        "                label=algorithms[i], marker='o', markersize=3)\n",
        "ax4.set_xlabel('Generation/Iteration')\n",
        "ax4.set_ylabel('Fitness Score')\n",
        "ax4.legend()\n",
        "ax4.grid(True, alpha=0.3)\n",
        "\n",
        "plt.tight_layout()\n",
        "plt.show()"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/",
          "height": 1000
        },
        "id": "6UyYdsMMTmnu",
        "outputId": "d61f6fd2-1431-493d-9448-eef7884c6e1d"
      },
      "execution_count": 10,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "\n",
            "============================================================\n",
            "📈 ALGORITHM COMPARISON\n",
            "============================================================\n",
            "🏆 Performance Comparison:\n",
            "Algorithm       Motif      Score    Info Content Runtime (s) \n",
            "-----------------------------------------------------------------\n",
            "GA              AAAAAAAA   5.7374   8.9281       0.82        \n",
            "PSO             ACATGTCA   1.3301   1.0695       0.29        \n",
            "Hybrid GA+PSO   AAAAAAAA   4.2190   7.5650       0.48        \n"
          ]
        },
        {
          "output_type": "display_data",
          "data": {
            "text/plain": [
              "<Figure size 1500x1000 with 4 Axes>"
            ],
            "image/png": "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\n"
          },
          "metadata": {}
        }
      ]
    },
    {
      "cell_type": "code",
      "source": [
        "# Motif visualization\n",
        "print(\"\\n🎨 Discovered Motifs Visualization:\")\n",
        "fig, axes = plt.subplots(1, 3, figsize=(15, 4))\n",
        "\n",
        "for i, (result, algorithm, color) in enumerate(zip(results, algorithms, colors)):\n",
        "    if result.pwm is not None:\n",
        "        # Create sequence logo-style visualization\n",
        "        pwm = result.pwm\n",
        "        ax = axes[i]\n",
        "\n",
        "        # Plot stacked bar chart for each position\n",
        "        nucleotides = ['A', 'T', 'G', 'C']\n",
        "        nuc_colors = ['red', 'blue', 'green', 'orange']\n",
        "\n",
        "        for pos in range(pwm.shape[1]):\n",
        "            bottom = 0\n",
        "            for nuc_idx, (nuc, nuc_color) in enumerate(zip(nucleotides, nuc_colors)):\n",
        "                height = pwm[nuc_idx, pos]\n",
        "                ax.bar(pos, height, bottom=bottom, color=nuc_color, alpha=0.7,\n",
        "                      width=0.8, edgecolor='black', linewidth=0.5)\n",
        "\n",
        "                # Add nucleotide labels\n",
        "                if height > 0.1:  # Only label if significant\n",
        "                    ax.text(pos, bottom + height/2, nuc, ha='center', va='center',\n",
        "                           fontweight='bold', fontsize=8)\n",
        "                bottom += height\n",
        "\n",
        "        ax.set_title(f'{algorithm}\\nMotif: {result.motif}')\n",
        "        ax.set_xlabel('Position')\n",
        "        ax.set_ylabel('Probability')\n",
        "        ax.set_xticks(range(motif_length))\n",
        "        ax.set_xticklabels([str(i+1) for i in range(motif_length)])\n",
        "        ax.grid(True, alpha=0.3)\n",
        "\n",
        "plt.tight_layout()\n",
        "plt.show()\n",
        "\n",
        "# Summary statistics\n",
        "print(\"\\n📊 Final Analysis Summary:\")\n",
        "print(f\"   Dataset: APP gene sequences\")\n",
        "print(f\"   Total sequences analyzed: {len(analysis_sequences)}\")\n",
        "print(f\"   Motif length: {motif_length} bp\")\n",
        "print(f\"   Best performing algorithm: {algorithms[np.argmax(scores)]}\")\n",
        "print(f\"   Best motif found: {results[np.argmax(scores)].motif}\")\n",
        "print(f\"   Best score achieved: {max(scores):.4f}\")\n",
        "print(f\"   Best information content: {max(ic_scores):.4f} bits\")\n",
        "\n",
        "print(\"\\n✅ Motif discovery analysis completed successfully!\")\n",
        "print(\"✅ Data loading functions defined!\")\n"
      ],
      "metadata": {
        "colab": {
          "base_uri": "https://localhost:8080/",
          "height": 620
        },
        "id": "SfUYdED3TuVK",
        "outputId": "13e8aab3-56d1-48be-c489-9f0aa874bcd4"
      },
      "execution_count": 11,
      "outputs": [
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "\n",
            "🎨 Discovered Motifs Visualization:\n"
          ]
        },
        {
          "output_type": "display_data",
          "data": {
            "text/plain": [
              "<Figure size 1500x400 with 3 Axes>"
            ],
            "image/png": "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\n"
          },
          "metadata": {}
        },
        {
          "output_type": "stream",
          "name": "stdout",
          "text": [
            "\n",
            "📊 Final Analysis Summary:\n",
            "   Dataset: APP gene sequences\n",
            "   Total sequences analyzed: 13\n",
            "   Motif length: 8 bp\n",
            "   Best performing algorithm: GA\n",
            "   Best motif found: AAAAAAAA\n",
            "   Best score achieved: 5.7374\n",
            "   Best information content: 8.9281 bits\n",
            "\n",
            "✅ Motif discovery analysis completed successfully!\n",
            "✅ Data loading functions defined!\n"
          ]
        }
      ]
    }
  ]
}