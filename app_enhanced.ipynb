{
  "nbformat": 4,
  "nbformat_minor": 0,
  "metadata": {
    "colab": {
      "provenance": []
    },
    "kernelspec": {
      "name": "python3",
      "display_name": "Python 3"
    },
    "language_info": {
      "name": "python"
    }
  },
  "cells": [
    {
      "cell_type": "markdown",
      "source": [
        "# Enhanced APP Gene Motif Discovery using GA and PSO\n",
        "## Optimized Implementation for Q1 Publication Quality\n",
        "\n",
        "This notebook implements state-of-the-art Genetic Algorithm (GA) and Particle Swarm Optimization (PSO) approaches for discovering regulatory motifs in APP gene sequences associated with Alzheimer's disease.\n",
        "\n",
        "**Key Features:**\n",
        "- Clean, publication-ready implementation\n",
        "- Enhanced GA with adaptive operators\n",
        "- Improved PSO with dynamic parameters\n",
        "- Comprehensive statistical validation\n",
        "- Publication-quality visualizations"
      ],
      "metadata": {
        "id": "title_cell"
      }
    },
    {
      "cell_type": "code",
      "source": [
        "# Enhanced APP Gene Motif Discovery - Core Imports and Configuration\n",
        "import os\n",
        "import time\n",
        "import warnings\n",
        "import random\n",
        "import math\n",
        "from pathlib import Path\n",
        "from typing import Dict, List, Any, Tuple, Optional\n",
        "from abc import ABC, abstractmethod\n",
        "from dataclasses import dataclass, field\n",
        "\n",
        "import numpy as np\n",
        "import pandas as pd\n",
        "from scipy import stats\n",
        "import matplotlib.pyplot as plt\n",
        "import seaborn as sns\n",
        "from tqdm.notebook import tqdm\n",
        "\n",
        "# Configuration\n",
        "warnings.filterwarnings('ignore')\n",
        "plt.style.use('seaborn-v0_8')\n",
        "sns.set_palette(\"husl\")\n",
        "\n",
        "# Reproducibility\n",
        "RANDOM_SEED = 42\n",
        "random.seed(RANDOM_SEED)\n",
        "np.random.seed(RANDOM_SEED)\n",
        "\n",
        "# Dataset configuration\n",
        "dataset_path = './Dataset_from reference genome'  # Update as needed\n",
        "\n",
        "print(\"🧬 Enhanced APP Gene Motif Discovery System\")\n",
        "print(\"📊 Publication-Quality Implementation\")\n",
        "print(f\"📁 Dataset path: {dataset_path}\")\n",
        "print(f\"📁 Dataset exists: {os.path.exists(dataset_path)}\")"
      ],
      "metadata": {
        "id": "imports_config"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ============================================================================\n",
        "# CORE DATA STRUCTURES\n",
        "# ============================================================================\n",
        "\n",
        "@dataclass\n",
        "class MotifResult:\n",
        "    \"\"\"Enhanced motif discovery result with comprehensive metrics.\"\"\"\n",
        "    motif: str\n",
        "    score: float\n",
        "    positions: List[int]\n",
        "    pwm: np.ndarray\n",
        "    information_content: float\n",
        "    algorithm: str\n",
        "    runtime: float\n",
        "    convergence_history: List[float]\n",
        "    \n",
        "    # Enhanced metrics for publication\n",
        "    conservation_score: float = 0.0\n",
        "    statistical_significance: float = 1.0\n",
        "    motif_instances: List[str] = field(default_factory=list)\n",
        "    sequence_coverage: float = 0.0\n",
        "    \n",
        "    def to_dict(self) -> Dict[str, Any]:\n",
        "        \"\"\"Convert to dictionary for analysis and export.\"\"\"\n",
        "        return {\n",
        "            'motif': self.motif,\n",
        "            'score': self.score,\n",
        "            'information_content': self.information_content,\n",
        "            'conservation_score': self.conservation_score,\n",
        "            'statistical_significance': self.statistical_significance,\n",
        "            'sequence_coverage': self.sequence_coverage,\n",
        "            'algorithm': self.algorithm,\n",
        "            'runtime': self.runtime,\n",
        "            'num_instances': len(self.motif_instances)\n",
        "        }\n",
        "\n",
        "@dataclass\n",
        "class OptimizationConfig:\n",
        "    \"\"\"Optimized configuration for GA and PSO algorithms.\"\"\"\n",
        "    # Common parameters\n",
        "    population_size: int = 50\n",
        "    max_generations: int = 100\n",
        "    convergence_threshold: float = 1e-6\n",
        "    max_stagnation: int = 15\n",
        "    \n",
        "    # GA-specific parameters\n",
        "    mutation_rate: float = 0.15\n",
        "    crossover_rate: float = 0.85\n",
        "    elitism_ratio: float = 0.1\n",
        "    tournament_size: int = 3\n",
        "    \n",
        "    # PSO-specific parameters\n",
        "    inertia_weight: float = 0.9\n",
        "    inertia_decay: float = 0.99\n",
        "    cognitive_coef: float = 2.0\n",
        "    social_coef: float = 2.0\n",
        "    velocity_clamp: float = 0.2\n",
        "    \n",
        "    def get_elitism_size(self) -> int:\n",
        "        \"\"\"Calculate elite population size.\"\"\"\n",
        "        return max(1, int(self.population_size * self.elitism_ratio))\n",
        "\n",
        "print(\"✅ Core data structures defined\")"
      ],
      "metadata": {
        "id": "data_structures"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ============================================================================\n",
        "# ENHANCED DATA LOADING AND PREPROCESSING\n",
        "# ============================================================================\n",
        "\n",
        "class DataLoader:\n",
        "    \"\"\"Enhanced data loader for APP genomic sequences.\"\"\"\n",
        "    \n",
        "    @staticmethod\n",
        "    def load_fasta_file(file_path: Path) -> List[str]:\n",
        "        \"\"\"Load sequences from FASTA file with validation.\"\"\"\n",
        "        sequences = []\n",
        "        try:\n",
        "            current_seq = \"\"\n",
        "            with open(file_path, 'r') as f:\n",
        "                for line in f:\n",
        "                    line = line.strip()\n",
        "                    if line.startswith('>'):\n",
        "                        if current_seq:\n",
        "                            sequences.append(current_seq.upper())\n",
        "                            current_seq = \"\"\n",
        "                    else:\n",
        "                        current_seq += line\n",
        "                \n",
        "                if current_seq:\n",
        "                    sequences.append(current_seq.upper())\n",
        "                    \n",
        "        except Exception as e:\n",
        "            print(f\"❌ Error loading {file_path}: {e}\")\n",
        "            return []\n",
        "        \n",
        "        return sequences\n",
        "    \n",
        "    @staticmethod\n",
        "    def validate_sequences(sequences: List[str], min_length: int = 100) -> List[str]:\n",
        "        \"\"\"Validate and filter sequences.\"\"\"\n",
        "        valid_sequences = []\n",
        "        valid_chars = set('ATGCN')\n",
        "        \n",
        "        for seq in sequences:\n",
        "            # Length check\n",
        "            if len(seq) < min_length:\n",
        "                continue\n",
        "            \n",
        "            # Character validation\n",
        "            if not set(seq).issubset(valid_chars):\n",
        "                continue\n",
        "            \n",
        "            # N content check (< 30%)\n",
        "            if seq.count('N') / len(seq) > 0.3:\n",
        "                continue\n",
        "                \n",
        "            valid_sequences.append(seq)\n",
        "        \n",
        "        return valid_sequences\n",
        "    \n",
        "    @classmethod\n",
        "    def load_app_data(cls, dataset_path: str, max_sequences: int = 50) -> Tuple[List[str], List[int]]:\n",
        "        \"\"\"Load and process APP genomic data.\"\"\"\n",
        "        print(\"🧬 Loading APP genomic data...\")\n",
        "        \n",
        "        sequences = []\n",
        "        labels = []\n",
        "        \n",
        "        # Load healthy sequences\n",
        "        healthy_file = Path(dataset_path) / 'APP_healthy_combined.fasta'\n",
        "        if healthy_file.exists():\n",
        "            healthy_seqs = cls.load_fasta_file(healthy_file)\n",
        "            healthy_seqs = cls.validate_sequences(healthy_seqs)\n",
        "            sequences.extend(healthy_seqs[:max_sequences//2])\n",
        "            labels.extend([0] * len(healthy_seqs[:max_sequences//2]))\n",
        "            print(f\"✅ Loaded {len(healthy_seqs[:max_sequences//2])} healthy sequences\")\n",
        "        \n",
        "        # Load unhealthy sequences\n",
        "        unhealthy_file = Path(dataset_path) / 'app_unhealthy_combined.fasta'\n",
        "        if unhealthy_file.exists():\n",
        "            unhealthy_seqs = cls.load_fasta_file(unhealthy_file)\n",
        "            unhealthy_seqs = cls.validate_sequences(unhealthy_seqs)\n",
        "            sequences.extend(unhealthy_seqs[:max_sequences//2])\n",
        "            labels.extend([1] * len(unhealthy_seqs[:max_sequences//2]))\n",
        "            print(f\"✅ Loaded {len(unhealthy_seqs[:max_sequences//2])} unhealthy sequences\")\n",
        "        \n",
        "        print(f\"📊 Total sequences: {len(sequences)}\")\n",
        "        if sequences:\n",
        "            avg_length = np.mean([len(s) for s in sequences])\n",
        "            print(f\"📏 Average length: {avg_length:.0f} bp\")\n",
        "        \n",
        "        return sequences, labels\n",
        "\n",
        "print(\"✅ Enhanced data loader defined\")"
      ],
      "metadata": {
        "id": "data_loader"
      },
      "execution_count": null,
      "outputs": []
    }
    },
    {
      "cell_type": "code",
      "source": [
        "# ============================================================================\n",
        "# BASE MOTIF DISCOVERY FRAMEWORK\n",
        "# ============================================================================\n",
        "\n",
        "class BaseMotifDiscovery(ABC):\n",
        "    \"\"\"Enhanced abstract base class for motif discovery algorithms.\"\"\"\n",
        "    \n",
        "    def __init__(self, sequences: List[str], motif_length: int, config: OptimizationConfig = None):\n",
        "        self.sequences = [seq.upper() for seq in sequences if seq]\n",
        "        self.motif_length = motif_length\n",
        "        self.config = config or OptimizationConfig()\n",
        "        self.nucleotides = ['A', 'T', 'G', 'C']\n",
        "        self.nucleotide_to_index = {nuc: idx for idx, nuc in enumerate(self.nucleotides)}\n",
        "        \n",
        "        # Validation\n",
        "        if not self.sequences:\n",
        "            raise ValueError(\"No valid sequences provided\")\n",
        "        if motif_length <= 0:\n",
        "            raise ValueError(\"Motif length must be positive\")\n",
        "    \n",
        "    @abstractmethod\n",
        "    def discover_motifs(self) -> MotifResult:\n",
        "        \"\"\"Abstract method to discover motifs.\"\"\"\n",
        "        pass\n",
        "    \n",
        "    def calculate_pwm(self, motif_instances: List[str]) -> np.ndarray:\n",
        "        \"\"\"Calculate Position Weight Matrix with pseudocounts.\"\"\"\n",
        "        if not motif_instances:\n",
        "            return np.ones((4, self.motif_length)) * 0.25\n",
        "        \n",
        "        pwm = np.zeros((4, self.motif_length))\n",
        "        pseudocount = 0.001\n",
        "        \n",
        "        for pos in range(self.motif_length):\n",
        "            for motif in motif_instances:\n",
        "                if pos < len(motif) and motif[pos] in self.nucleotide_to_index:\n",
        "                    pwm[self.nucleotide_to_index[motif[pos]], pos] += 1\n",
        "            \n",
        "            # Add pseudocounts and normalize\n",
        "            pwm[:, pos] += pseudocount\n",
        "            pwm[:, pos] /= np.sum(pwm[:, pos])\n",
        "        \n",
        "        return pwm\n",
        "    \n",
        "    def calculate_information_content(self, pwm: np.ndarray) -> float:\n",
        "        \"\"\"Calculate information content of PWM.\"\"\"\n",
        "        if pwm is None or pwm.size == 0:\n",
        "            return 0.0\n",
        "        \n",
        "        ic_total = 0.0\n",
        "        for pos in range(pwm.shape[1]):\n",
        "            ic_pos = 0.0\n",
        "            for nuc_prob in pwm[:, pos]:\n",
        "                if nuc_prob > 0:\n",
        "                    ic_pos += nuc_prob * np.log2(nuc_prob / 0.25)\n",
        "            ic_total += ic_pos\n",
        "        \n",
        "        return ic_total\n",
        "    \n",
        "    def calculate_conservation_score(self, motif_instances: List[str]) -> float:\n",
        "        \"\"\"Calculate conservation score based on sequence similarity.\"\"\"\n",
        "        if len(motif_instances) < 2:\n",
        "            return 0.0\n",
        "        \n",
        "        total_similarity = 0.0\n",
        "        comparisons = 0\n",
        "        \n",
        "        for i in range(len(motif_instances)):\n",
        "            for j in range(i + 1, len(motif_instances)):\n",
        "                seq1, seq2 = motif_instances[i], motif_instances[j]\n",
        "                if len(seq1) == len(seq2):\n",
        "                    similarity = sum(1 for a, b in zip(seq1, seq2) if a == b) / len(seq1)\n",
        "                    total_similarity += similarity\n",
        "                    comparisons += 1\n",
        "        \n",
        "        return total_similarity / comparisons if comparisons > 0 else 0.0\n",
        "    \n",
        "    def get_consensus_motif(self, motif_instances: List[str]) -> str:\n",
        "        \"\"\"Generate consensus motif from instances.\"\"\"\n",
        "        if not motif_instances:\n",
        "            return \"A\" * self.motif_length\n",
        "        \n",
        "        consensus = \"\"\n",
        "        for pos in range(self.motif_length):\n",
        "            nucleotide_counts = {'A': 0, 'T': 0, 'G': 0, 'C': 0}\n",
        "            \n",
        "            for motif in motif_instances:\n",
        "                if pos < len(motif) and motif[pos] in nucleotide_counts:\n",
        "                    nucleotide_counts[motif[pos]] += 1\n",
        "            \n",
        "            consensus += max(nucleotide_counts, key=nucleotide_counts.get)\n",
        "        \n",
        "        return consensus\n",
        "    \n",
        "    def calculate_fitness(self, positions: List[int]) -> Tuple[float, List[str]]:\n",
        "        \"\"\"Enhanced fitness calculation with multiple criteria.\"\"\"\n",
        "        # Extract motif instances\n",
        "        motif_instances = []\n",
        "        for i, pos in enumerate(positions):\n",
        "            if i < len(self.sequences) and pos + self.motif_length <= len(self.sequences[i]):\n",
        "                instance = self.sequences[i][pos:pos + self.motif_length]\n",
        "                motif_instances.append(instance)\n",
        "        \n",
        "        if not motif_instances:\n",
        "            return 0.0, []\n",
        "        \n",
        "        # Calculate components\n",
        "        pwm = self.calculate_pwm(motif_instances)\n",
        "        ic = self.calculate_information_content(pwm)\n",
        "        conservation = self.calculate_conservation_score(motif_instances)\n",
        "        coverage = len(motif_instances) / len(self.sequences)\n",
        "        \n",
        "        # Combined fitness with balanced weights\n",
        "        fitness = (0.4 * ic + 0.4 * conservation + 0.2 * coverage)\n",
        "        \n",
        "        return fitness, motif_instances\n",
        "\n",
        "print(\"✅ Base motif discovery framework defined\")"
      ],
      "metadata": {
        "id": "base_framework"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ============================================================================\n",
        "# ENHANCED GENETIC ALGORITHM IMPLEMENTATION\n",
        "# ============================================================================\n",
        "\n",
        "class Individual:\n",
        "    \"\"\"Enhanced individual representation for GA.\"\"\"\n",
        "    \n",
        "    def __init__(self, sequences: List[str], motif_length: int):\n",
        "        self.sequences = sequences\n",
        "        self.motif_length = motif_length\n",
        "        self.positions = self._initialize_positions()\n",
        "        self.fitness = 0.0\n",
        "        self.motif_instances = []\n",
        "    \n",
        "    def _initialize_positions(self) -> List[int]:\n",
        "        \"\"\"Initialize random positions for each sequence.\"\"\"\n",
        "        positions = []\n",
        "        for seq in self.sequences:\n",
        "            if len(seq) >= self.motif_length:\n",
        "                max_pos = len(seq) - self.motif_length\n",
        "                positions.append(random.randint(0, max_pos))\n",
        "            else:\n",
        "                positions.append(0)\n",
        "        return positions\n",
        "    \n",
        "    def mutate(self, mutation_rate: float):\n",
        "        \"\"\"Enhanced mutation with adaptive step size.\"\"\"\n",
        "        for i in range(len(self.positions)):\n",
        "            if random.random() < mutation_rate:\n",
        "                seq_length = len(self.sequences[i])\n",
        "                if seq_length >= self.motif_length:\n",
        "                    max_pos = seq_length - self.motif_length\n",
        "                    # Adaptive mutation: small changes more likely\n",
        "                    if random.random() < 0.7:  # Local search\n",
        "                        delta = random.randint(-5, 5)\n",
        "                        new_pos = max(0, min(max_pos, self.positions[i] + delta))\n",
        "                    else:  # Global search\n",
        "                        new_pos = random.randint(0, max_pos)\n",
        "                    self.positions[i] = new_pos\n",
        "    \n",
        "    def copy(self):\n",
        "        \"\"\"Create a deep copy of this individual.\"\"\"\n",
        "        new_individual = Individual(self.sequences, self.motif_length)\n",
        "        new_individual.positions = self.positions.copy()\n",
        "        new_individual.fitness = self.fitness\n",
        "        new_individual.motif_instances = self.motif_instances.copy()\n",
        "        return new_individual\n",
        "\n",
        "class EnhancedGeneticAlgorithm(BaseMotifDiscovery):\n",
        "    \"\"\"Enhanced Genetic Algorithm with adaptive operators.\"\"\"\n",
        "    \n",
        "    def __init__(self, sequences: List[str], motif_length: int, config: OptimizationConfig = None):\n",
        "        super().__init__(sequences, motif_length, config)\n",
        "        self.population = []\n",
        "        self.best_individual = None\n",
        "        self.convergence_history = []\n",
        "    \n",
        "    def discover_motifs(self) -> MotifResult:\n",
        "        \"\"\"Main GA motif discovery algorithm.\"\"\"\n",
        "        start_time = time.time()\n",
        "        print(f\"\\n🧬 Starting Enhanced Genetic Algorithm...\")\n",
        "        print(f\"   Population: {self.config.population_size}, Generations: {self.config.max_generations}\")\n",
        "        \n",
        "        try:\n",
        "            # Initialize population\n",
        "            self._initialize_population()\n",
        "            \n",
        "            # Evolution loop\n",
        "            stagnation_count = 0\n",
        "            best_fitness = 0.0\n",
        "            \n",
        "            for generation in range(self.config.max_generations):\n",
        "                # Evaluate population\n",
        "                self._evaluate_population()\n",
        "                \n",
        "                # Track best individual\n",
        "                current_best = max(self.population, key=lambda x: x.fitness)\n",
        "                self.convergence_history.append(current_best.fitness)\n",
        "                \n",
        "                # Check for improvement\n",
        "                if current_best.fitness > best_fitness + self.config.convergence_threshold:\n",
        "                    best_fitness = current_best.fitness\n",
        "                    self.best_individual = current_best.copy()\n",
        "                    stagnation_count = 0\n",
        "                else:\n",
        "                    stagnation_count += 1\n",
        "                \n",
        "                # Progress update\n",
        "                if generation % 20 == 0 or generation < 5:\n",
        "                    print(f\"   Gen {generation:3d}: Best fitness = {best_fitness:.4f}\")\n",
        "                \n",
        "                # Check convergence\n",
        "                if stagnation_count >= self.config.max_stagnation:\n",
        "                    print(f\"   Converged after {generation + 1} generations\")\n",
        "                    break\n",
        "                \n",
        "                # Create next generation\n",
        "                self._create_next_generation()\n",
        "            \n",
        "            # Return results\n",
        "            return self._create_result(start_time)\n",
        "            \n",
        "        except Exception as e:\n",
        "            print(f\"❌ GA error: {e}\")\n",
        "            return self._create_dummy_result(start_time)\n",
        "    \n",
        "    def _initialize_population(self):\n",
        "        \"\"\"Initialize diverse population.\"\"\"\n",
        "        self.population = []\n",
        "        for _ in range(self.config.population_size):\n",
        "            individual = Individual(self.sequences, self.motif_length)\n",
        "            self.population.append(individual)\n",
        "    \n",
        "    def _evaluate_population(self):\n",
        "        \"\"\"Evaluate fitness for all individuals.\"\"\"\n",
        "        for individual in self.population:\n",
        "            fitness, motif_instances = self.calculate_fitness(individual.positions)\n",
        "            individual.fitness = fitness\n",
        "            individual.motif_instances = motif_instances\n",
        "    \n",
        "    def _create_next_generation(self):\n",
        "        \"\"\"Create next generation with enhanced operators.\"\"\"\n",
        "        # Sort by fitness\n",
        "        self.population.sort(key=lambda x: x.fitness, reverse=True)\n",
        "        \n",
        "        new_population = []\n",
        "        \n",
        "        # Elitism - keep best individuals\n",
        "        elite_size = self.config.get_elitism_size()\n",
        "        for i in range(elite_size):\n",
        "            new_population.append(self.population[i].copy())\n",
        "        \n",
        "        # Generate offspring\n",
        "        while len(new_population) < self.config.population_size:\n",
        "            # Tournament selection\n",
        "            parent1 = self._tournament_selection()\n",
        "            parent2 = self._tournament_selection()\n",
        "            \n",
        "            # Crossover\n",
        "            if random.random() < self.config.crossover_rate:\n",
        "                child1, child2 = self._enhanced_crossover(parent1, parent2)\n",
        "            else:\n",
        "                child1, child2 = parent1.copy(), parent2.copy()\n",
        "            \n",
        "            # Mutation\n",
        "            child1.mutate(self.config.mutation_rate)\n",
        "            child2.mutate(self.config.mutation_rate)\n",
        "            \n",
        "            new_population.extend([child1, child2])\n",
        "        \n",
        "        # Trim to exact size\n",
        "        self.population = new_population[:self.config.population_size]\n",
        "    \n",
        "    def _tournament_selection(self) -> Individual:\n",
        "        \"\"\"Tournament selection with configurable size.\"\"\"\n",
        "        tournament = random.sample(self.population, \n",
        "                                 min(self.config.tournament_size, len(self.population)))\n",
        "        return max(tournament, key=lambda x: x.fitness)\n",
        "    \n",
        "    def _enhanced_crossover(self, parent1: Individual, parent2: Individual) -> Tuple[Individual, Individual]:\n",
        "        \"\"\"Enhanced crossover with multiple strategies.\"\"\"\n",
        "        if len(parent1.positions) != len(parent2.positions):\n",
        "            return parent1.copy(), parent2.copy()\n",
        "        \n",
        "        child1 = Individual(self.sequences, self.motif_length)\n",
        "        child2 = Individual(self.sequences, self.motif_length)\n",
        "        \n",
        "        # Uniform crossover with bias toward better parent\n",
        "        better_parent = parent1 if parent1.fitness > parent2.fitness else parent2\n",
        "        worse_parent = parent2 if parent1.fitness > parent2.fitness else parent1\n",
        "        \n",
        "        for i in range(len(parent1.positions)):\n",
        "            if random.random() < 0.7:  # Bias toward better parent\n",
        "                child1.positions[i] = better_parent.positions[i]\n",
        "                child2.positions[i] = worse_parent.positions[i]\n",
        "            else:\n",
        "                child1.positions[i] = worse_parent.positions[i]\n",
        "                child2.positions[i] = better_parent.positions[i]\n",
        "        \n",
        "        return child1, child2\n",
        "    \n",
        "    def _create_result(self, start_time: float) -> MotifResult:\n",
        "        \"\"\"Create comprehensive result object.\"\"\"\n",
        "        if not self.best_individual:\n",
        "            return self._create_dummy_result(start_time)\n",
        "        \n",
        "        runtime = time.time() - start_time\n",
        "        motif_instances = self.best_individual.motif_instances\n",
        "        consensus_motif = self.get_consensus_motif(motif_instances)\n",
        "        pwm = self.calculate_pwm(motif_instances)\n",
        "        ic = self.calculate_information_content(pwm)\n",
        "        conservation = self.calculate_conservation_score(motif_instances)\n",
        "        coverage = len(motif_instances) / len(self.sequences)\n",
        "        \n",
        "        print(f\"✅ GA completed! Best motif: {consensus_motif}\")\n",
        "        print(f\"   Final fitness: {self.best_individual.fitness:.4f}\")\n",
        "        print(f\"   Runtime: {runtime:.2f}s\")\n",
        "        \n",
        "        return MotifResult(\n",
        "            motif=consensus_motif,\n",
        "            score=self.best_individual.fitness,\n",
        "            positions=self.best_individual.positions,\n",
        "            pwm=pwm,\n",
        "            information_content=ic,\n",
        "            algorithm=\"Enhanced_GA\",\n",
        "            runtime=runtime,\n",
        "            convergence_history=self.convergence_history,\n",
        "            conservation_score=conservation,\n",
        "            motif_instances=motif_instances,\n",
        "            sequence_coverage=coverage\n",
        "        )\n",
        "    \n",
        "    def _create_dummy_result(self, start_time: float) -> MotifResult:\n",
        "        \"\"\"Create dummy result for failed runs.\"\"\"\n",
        "        runtime = time.time() - start_time\n",
        "        dummy_motif = \"A\" * self.motif_length\n",
        "        dummy_pwm = np.ones((4, self.motif_length)) * 0.25\n",
        "        \n",
        "        return MotifResult(\n",
        "            motif=dummy_motif,\n",
        "            score=0.0,\n",
        "            positions=[0] * len(self.sequences),\n",
        "            pwm=dummy_pwm,\n",
        "            information_content=0.0,\n",
        "            algorithm=\"Enhanced_GA\",\n",
        "            runtime=runtime,\n",
        "            convergence_history=[0.0]\n",
        "        )\n",
        "\n",
        "print(\"✅ Enhanced Genetic Algorithm implemented\")"
      ],
      "metadata": {
        "id": "enhanced_ga"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ============================================================================\n",
        "# ENHANCED PARTICLE SWARM OPTIMIZATION IMPLEMENTATION\n",
        "# ============================================================================\n",
        "\n",
        "class Particle:\n",
        "    \"\"\"Enhanced particle representation for PSO.\"\"\"\n",
        "    \n",
        "    def __init__(self, sequences: List[str], motif_length: int):\n",
        "        self.sequences = sequences\n",
        "        self.motif_length = motif_length\n",
        "        \n",
        "        # Position and velocity (continuous values)\n",
        "        self.position = self._initialize_position()\n",
        "        self.velocity = self._initialize_velocity()\n",
        "        \n",
        "        # Personal best\n",
        "        self.best_position = self.position.copy()\n",
        "        self.best_fitness = 0.0\n",
        "        \n",
        "        # Current state\n",
        "        self.fitness = 0.0\n",
        "        self.motif_instances = []\n",
        "    \n",
        "    def _initialize_position(self) -> List[float]:\n",
        "        \"\"\"Initialize random continuous positions.\"\"\"\n",
        "        positions = []\n",
        "        for seq in self.sequences:\n",
        "            if len(seq) >= self.motif_length:\n",
        "                max_pos = len(seq) - self.motif_length\n",
        "                positions.append(random.uniform(0, max_pos))\n",
        "            else:\n",
        "                positions.append(0.0)\n",
        "        return positions\n",
        "    \n",
        "    def _initialize_velocity(self) -> List[float]:\n",
        "        \"\"\"Initialize random velocities.\"\"\"\n",
        "        velocities = []\n",
        "        for seq in self.sequences:\n",
        "            if len(seq) >= self.motif_length:\n",
        "                max_pos = len(seq) - self.motif_length\n",
        "                # Small initial velocities\n",
        "                velocities.append(random.uniform(-max_pos * 0.1, max_pos * 0.1))\n",
        "            else:\n",
        "                velocities.append(0.0)\n",
        "        return velocities\n",
        "    \n",
        "    def get_discrete_positions(self) -> List[int]:\n",
        "        \"\"\"Convert continuous positions to discrete positions.\"\"\"\n",
        "        discrete_positions = []\n",
        "        for i, pos in enumerate(self.position):\n",
        "            seq_length = len(self.sequences[i])\n",
        "            if seq_length >= self.motif_length:\n",
        "                max_pos = seq_length - self.motif_length\n",
        "                discrete_pos = max(0, min(int(round(pos)), max_pos))\n",
        "                discrete_positions.append(discrete_pos)\n",
        "            else:\n",
        "                discrete_positions.append(0)\n",
        "        return discrete_positions\n",
        "    \n",
        "    def update_velocity(self, global_best_position: List[float], \n",
        "                       inertia: float, cognitive_coef: float, social_coef: float,\n",
        "                       velocity_clamp: float):\n",
        "        \"\"\"Enhanced velocity update with clamping.\"\"\"\n",
        "        for i in range(len(self.velocity)):\n",
        "            # Inertia component\n",
        "            inertia_component = inertia * self.velocity[i]\n",
        "            \n",
        "            # Cognitive component (personal best)\n",
        "            cognitive_component = (cognitive_coef * random.random() * \n",
        "                                 (self.best_position[i] - self.position[i]))\n",
        "            \n",
        "            # Social component (global best)\n",
        "            social_component = (social_coef * random.random() * \n",
        "                              (global_best_position[i] - self.position[i]))\n",
        "            \n",
        "            # Update velocity\n",
        "            self.velocity[i] = inertia_component + cognitive_component + social_component\n",
        "            \n",
        "            # Velocity clamping\n",
        "            seq_length = len(self.sequences[i])\n",
        "            if seq_length >= self.motif_length:\n",
        "                max_velocity = (seq_length - self.motif_length) * velocity_clamp\n",
        "                self.velocity[i] = max(-max_velocity, min(max_velocity, self.velocity[i]))\n",
        "    \n",
        "    def update_position(self):\n",
        "        \"\"\"Update particle position with boundary constraints.\"\"\"\n",
        "        for i in range(len(self.position)):\n",
        "            self.position[i] += self.velocity[i]\n",
        "            \n",
        "            # Position bounds\n",
        "            seq_length = len(self.sequences[i])\n",
        "            if seq_length >= self.motif_length:\n",
        "                max_pos = seq_length - self.motif_length\n",
        "                self.position[i] = max(0.0, min(float(max_pos), self.position[i]))\n",
        "    \n",
        "    def update_personal_best(self):\n",
        "        \"\"\"Update personal best if current fitness is better.\"\"\"\n",
        "        if self.fitness > self.best_fitness:\n",
        "            self.best_fitness = self.fitness\n",
        "            self.best_position = self.position.copy()\n",
        "\n",
        "class EnhancedParticleSwarmOptimization(BaseMotifDiscovery):\n",
        "    \"\"\"Enhanced Particle Swarm Optimization with dynamic parameters.\"\"\"\n",
        "    \n",
        "    def __init__(self, sequences: List[str], motif_length: int, config: OptimizationConfig = None):\n",
        "        super().__init__(sequences, motif_length, config)\n",
        "        self.swarm = []\n",
        "        self.global_best_particle = None\n",
        "        self.global_best_position = []\n",
        "        self.global_best_fitness = 0.0\n",
        "        self.convergence_history = []\n",
        "        self.current_inertia = self.config.inertia_weight\n",
        "    \n",
        "    def discover_motifs(self) -> MotifResult:\n",
        "        \"\"\"Main PSO motif discovery algorithm.\"\"\"\n",
        "        start_time = time.time()\n",
        "        print(f\"\\n🐦 Starting Enhanced Particle Swarm Optimization...\")\n",
        "        print(f\"   Swarm size: {self.config.population_size}, Iterations: {self.config.max_generations}\")\n",
        "        \n",
        "        try:\n",
        "            # Initialize swarm\n",
        "            self._initialize_swarm()\n",
        "            \n",
        "            # PSO loop\n",
        "            stagnation_count = 0\n",
        "            \n",
        "            for iteration in range(self.config.max_generations):\n",
        "                # Evaluate swarm\n",
        "                self._evaluate_swarm()\n",
        "                \n",
        "                # Track convergence\n",
        "                self.convergence_history.append(self.global_best_fitness)\n",
        "                \n",
        "                # Check for stagnation\n",
        "                if len(self.convergence_history) > 1:\n",
        "                    improvement = abs(self.convergence_history[-1] - self.convergence_history[-2])\n",
        "                    if improvement < self.config.convergence_threshold:\n",
        "                        stagnation_count += 1\n",
        "                    else:\n",
        "                        stagnation_count = 0\n",
        "                \n",
        "                # Progress update\n",
        "                if iteration % 20 == 0 or iteration < 5:\n",
        "                    print(f\"   Iter {iteration:3d}: Best fitness = {self.global_best_fitness:.4f}\")\n",
        "                \n",
        "                # Check convergence\n",
        "                if stagnation_count >= self.config.max_stagnation:\n",
        "                    print(f\"   Converged after {iteration + 1} iterations\")\n",
        "                    break\n",
        "                \n",
        "                # Update swarm\n",
        "                self._update_swarm()\n",
        "                \n",
        "                # Decay inertia\n",
        "                self.current_inertia *= self.config.inertia_decay\n",
        "            \n",
        "            # Return results\n",
        "            return self._create_result(start_time)\n",
        "            \n",
        "        except Exception as e:\n",
        "            print(f\"❌ PSO error: {e}\")\n",
        "            return self._create_dummy_result(start_time)\n",
        "    \n",
        "    def _initialize_swarm(self):\n",
        "        \"\"\"Initialize diverse swarm.\"\"\"\n",
        "        self.swarm = []\n",
        "        for _ in range(self.config.population_size):\n",
        "            particle = Particle(self.sequences, self.motif_length)\n",
        "            self.swarm.append(particle)\n",
        "    \n",
        "    def _evaluate_swarm(self):\n",
        "        \"\"\"Evaluate fitness for all particles and update global best.\"\"\"\n",
        "        for particle in self.swarm:\n",
        "            discrete_positions = particle.get_discrete_positions()\n",
        "            fitness, motif_instances = self.calculate_fitness(discrete_positions)\n",
        "            particle.fitness = fitness\n",
        "            particle.motif_instances = motif_instances\n",
        "            \n",
        "            # Update personal best\n",
        "            particle.update_personal_best()\n",
        "            \n",
        "            # Update global best\n",
        "            if particle.fitness > self.global_best_fitness:\n",
        "                self.global_best_fitness = particle.fitness\n",
        "                self.global_best_particle = particle\n",
        "                self.global_best_position = particle.position.copy()\n",
        "    \n",
        "    def _update_swarm(self):\n",
        "        \"\"\"Update velocities and positions of all particles.\"\"\"\n",
        "        for particle in self.swarm:\n",
        "            # Update velocity\n",
        "            particle.update_velocity(\n",
        "                self.global_best_position,\n",
        "                self.current_inertia,\n",
        "                self.config.cognitive_coef,\n",
        "                self.config.social_coef,\n",
        "                self.config.velocity_clamp\n",
        "            )\n",
        "            \n",
        "            # Update position\n",
        "            particle.update_position()\n",
        "    \n",
        "    def _create_result(self, start_time: float) -> MotifResult:\n",
        "        \"\"\"Create comprehensive result object.\"\"\"\n",
        "        if not self.global_best_particle:\n",
        "            return self._create_dummy_result(start_time)\n",
        "        \n",
        "        runtime = time.time() - start_time\n",
        "        motif_instances = self.global_best_particle.motif_instances\n",
        "        consensus_motif = self.get_consensus_motif(motif_instances)\n",
        "        pwm = self.calculate_pwm(motif_instances)\n",
        "        ic = self.calculate_information_content(pwm)\n",
        "        conservation = self.calculate_conservation_score(motif_instances)\n",
        "        coverage = len(motif_instances) / len(self.sequences)\n",
        "        discrete_positions = self.global_best_particle.get_discrete_positions()\n",
        "        \n",
        "        print(f\"✅ PSO completed! Best motif: {consensus_motif}\")\n",
        "        print(f\"   Final fitness: {self.global_best_fitness:.4f}\")\n",
        "        print(f\"   Runtime: {runtime:.2f}s\")\n",
        "        \n",
        "        return MotifResult(\n",
        "            motif=consensus_motif,\n",
        "            score=self.global_best_fitness,\n",
        "            positions=discrete_positions,\n",
        "            pwm=pwm,\n",
        "            information_content=ic,\n",
        "            algorithm=\"Enhanced_PSO\",\n",
        "            runtime=runtime,\n",
        "            convergence_history=self.convergence_history,\n",
        "            conservation_score=conservation,\n",
        "            motif_instances=motif_instances,\n",
        "            sequence_coverage=coverage\n",
        "        )\n",
        "    \n",
        "    def _create_dummy_result(self, start_time: float) -> MotifResult:\n",
        "        \"\"\"Create dummy result for failed runs.\"\"\"\n",
        "        runtime = time.time() - start_time\n",
        "        dummy_motif = \"A\" * self.motif_length\n",
        "        dummy_pwm = np.ones((4, self.motif_length)) * 0.25\n",
        "        \n",
        "        return MotifResult(\n",
        "            motif=dummy_motif,\n",
        "            score=0.0,\n",
        "            positions=[0] * len(self.sequences),\n",
        "            pwm=dummy_pwm,\n",
        "            information_content=0.0,\n",
        "            algorithm=\"Enhanced_PSO\",\n",
        "            runtime=runtime,\n",
        "            convergence_history=[0.0]\n",
        "        )\n",
        "\n",
        "print(\"✅ Enhanced Particle Swarm Optimization implemented\")"
      ],
      "metadata": {
        "id": "enhanced_pso"
      },
      "execution_count": null,
      "outputs": []
    },
    {
      "cell_type": "code",
      "source": [
        "# ============================================================================\n",
        "# COMPREHENSIVE EVALUATION AND COMPARISON FRAMEWORK\n",
        "# ============================================================================\n",
        "\n",
        "class MotifEvaluator:\n",
        "    \"\"\"Comprehensive evaluation framework for motif discovery results.\"\"\"\n",
        "    \n",
        "    @staticmethod\n",
        "    def compare_algorithms(results: List[MotifResult], num_runs: int = 5) -> pd.DataFrame:\n",
        "        \"\"\"Compare multiple algorithm results with statistical analysis.\"\"\"\n",
        "        comparison_data = []\n",
        "        \n",
        "        for result in results:\n",
        "            comparison_data.append({\n",
        "                'Algorithm': result.algorithm,\n",
        "                'Best_Score': result.score,\n",
        "                'Information_Content': result.information_content,\n",
        "                'Conservation_Score': result.conservation_score,\n",
        "                'Sequence_Coverage': result.sequence_coverage,\n",
        "                'Runtime': result.runtime,\n",
        "                'Convergence_Generations': len(result.convergence_history),\n",
        "                'Final_Motif': result.motif\n",
        "            })\n",
        "        \n",
        "        return pd.DataFrame(comparison_data)\n",
        "    \n",
        "    @staticmethod\n",
        "    def statistical_significance_test(results1: List[float], results2: List[float]) -> Tuple[float, float]:\n",
        "        \"\"\"Perform statistical significance test between two sets of results.\"\"\"\n",
        "        if len(results1) < 2 or len(results2) < 2:\n",
        "            return 0.0, 1.0\n",
        "        \n",
        "        # Perform t-test\n",
        "        t_stat, p_value = stats.ttest_ind(results1, results2)\n",
        "        return t_stat, p_value\n",
        "    \n",
        "    @staticmethod\n",
        "    def calculate_effect_size(results1: List[float], results2: List[float]) -> float:\n",
        "        \"\"\"Calculate Cohen's d effect size.\"\"\"\n",
        "        if len(results1) < 2 or len(results2) < 2:\n",
        "            return 0.0\n",
        "        \n",
        "        mean1, mean2 = np.mean(results1), np.mean(results2)\n",
        "        std1, std2 = np.std(results1, ddof=1), np.std(results2, ddof=1)\n",
        "        \n",
        "        # Pooled standard deviation\n",
        "        pooled_std = np.sqrt(((len(results1) - 1) * std1**2 + (len(results2) - 1) * std2**2) / \n",
        "                           (len(results1) + len(results2) - 2))\n",
        "        \n",
        "        if pooled_std == 0:\n",
        "            return 0.0\n",
        "        \n",
        "        return (mean1 - mean2) / pooled_std\n",
        "\n",
        "class MotifVisualizer:\n",
        "    \"\"\"Publication-quality visualization for motif discovery results.\"\"\"\n",
        "    \n",
        "    @staticmethod\n",
        "    def plot_convergence_comparison(results: List[MotifResult], figsize: Tuple[int, int] = (12, 8)):\n",
        "        \"\"\"Plot convergence curves for algorithm comparison.\"\"\"\n",
        "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)\n",
        "        \n",
        "        # Convergence curves\n",
        "        for result in results:\n",
        "            generations = range(len(result.convergence_history))\n",
        "            ax1.plot(generations, result.convergence_history, \n",
        "                    label=result.algorithm, linewidth=2, marker='o', markersize=3)\n",
        "        \n",
        "        ax1.set_xlabel('Generation/Iteration', fontsize=12)\n",
        "        ax1.set_ylabel('Fitness Score', fontsize=12)\n",
        "        ax1.set_title('Algorithm Convergence Comparison', fontsize=14, fontweight='bold')\n",
        "        ax1.legend(fontsize=10)\n",
        "        ax1.grid(True, alpha=0.3)\n",
        "        \n",
        "        # Performance metrics comparison\n",
        "        algorithms = [r.algorithm for r in results]\n",
        "        scores = [r.score for r in results]\n",
        "        ic_scores = [r.information_content for r in results]\n",
        "        conservation_scores = [r.conservation_score for r in results]\n",
        "        \n",
        "        x = np.arange(len(algorithms))\n",
        "        width = 0.25\n",
        "        \n",
        "        ax2.bar(x - width, scores, width, label='Overall Score', alpha=0.8)\n",
        "        ax2.bar(x, ic_scores, width, label='Information Content', alpha=0.8)\n",
        "        ax2.bar(x + width, conservation_scores, width, label='Conservation', alpha=0.8)\n",
        "        \n",
        "        ax2.set_xlabel('Algorithm', fontsize=12)\n",
        "        ax2.set_ylabel('Score', fontsize=12)\n",
        "        ax2.set_title('Performance Metrics Comparison', fontsize=14, fontweight='bold')\n",
        "        ax2.set_xticks(x)\n",
        "        ax2.set_xticklabels(algorithms, rotation=45)\n",
        "        ax2.legend(fontsize=10)\n",
        "        ax2.grid(True, alpha=0.3, axis='y')\n",
        "        \n",
        "        plt.tight_layout()\n",
        "        plt.show()\n",
        "    \n",
        "    @staticmethod\n",
        "    def plot_motif_logo(pwm: np.ndarray, motif: str, title: str = \"Motif Logo\", \n",
        "                       figsize: Tuple[int, int] = (10, 4)):\n",
        "        \"\"\"Create a motif logo visualization.\"\"\"\n",
        "        fig, ax = plt.subplots(figsize=figsize)\n",
        "        \n",
        "        nucleotides = ['A', 'T', 'G', 'C']\n",
        "        colors = ['red', 'blue', 'orange', 'green']\n",
        "        \n",
        "        positions = range(pwm.shape[1])\n",
        "        \n",
        "        # Calculate information content for each position\n",
        "        ic_per_position = []\n",
        "        for pos in range(pwm.shape[1]):\n",
        "            ic_pos = 0.0\n",
        "            for nuc_prob in pwm[:, pos]:\n",
        "                if nuc_prob > 0:\n",
        "                    ic_pos += nuc_prob * np.log2(nuc_prob / 0.25)\n",
        "            ic_per_position.append(ic_pos)\n",
        "        \n",
        "        # Create stacked bar chart\n",
        "        bottom = np.zeros(len(positions))\n",
        "        \n",
        "        for i, (nuc, color) in enumerate(zip(nucleotides, colors)):\n",
        "            heights = pwm[i, :] * ic_per_position\n",
        "            ax.bar(positions, heights, bottom=bottom, color=color, \n",
        "                  label=nuc, alpha=0.8, edgecolor='black', linewidth=0.5)\n",
        "            \n",
        "            # Add nucleotide labels\n",
        "            for pos, height in enumerate(heights):\n",
        "                if height > 0.1:  # Only label significant contributions\n",
        "                    ax.text(pos, bottom[pos] + height/2, nuc, \n",
        "                           ha='center', va='center', fontweight='bold', fontsize=12)\n",
        "            \n",
        "            bottom += heights\n",
        "        \n",
        "        ax.set_xlabel('Position', fontsize=12)\n",
        "        ax.set_ylabel('Information Content (bits)', fontsize=12)\n",
        "        ax.set_title(f'{title}\\nConsensus: {motif}', fontsize=14, fontweight='bold')\n",
        "        ax.set_xticks(positions)\n",
        "        ax.set_xticklabels([str(i+1) for i in positions])\n",
        "        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n",
        "        ax.grid(True, alpha=0.3, axis='y')\n",
        "        \n",
        "        plt.tight_layout()\n",
        "        plt.show()\n",
        "    \n",
        "    @staticmethod\n",
        "    def plot_runtime_analysis(results: List[MotifResult], figsize: Tuple[int, int] = (10, 6)):\n",
        "        \"\"\"Analyze and visualize runtime performance.\"\"\"\n",
        "        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=figsize)\n",
        "        \n",
        "        algorithms = [r.algorithm for r in results]\n",
        "        runtimes = [r.runtime for r in results]\n",
        "        scores = [r.score for r in results]\n",
        "        \n",
        "        # Runtime comparison\n",
        "        bars = ax1.bar(algorithms, runtimes, alpha=0.7, \n",
        "                      color=['skyblue', 'lightcoral', 'lightgreen'][:len(algorithms)])\n",
        "        ax1.set_ylabel('Runtime (seconds)', fontsize=12)\n",
        "        ax1.set_title('Algorithm Runtime Comparison', fontsize=14, fontweight='bold')\n",
        "        ax1.tick_params(axis='x', rotation=45)\n",
        "        \n",
        "        # Add value labels on bars\n",
        "        for bar, runtime in zip(bars, runtimes):\n",
        "            ax1.text(bar.get_x() + bar.get_width()/2., bar.get_height() + max(runtimes)*0.01,\n",
        "                    f'{runtime:.2f}s', ha='center', va='bottom', fontweight='bold')\n",
        "        \n",
        "        # Score vs Runtime scatter\n",
        "        colors = ['blue', 'red', 'green'][:len(algorithms)]\n",
        "        for i, (alg, runtime, score) in enumerate(zip(algorithms, runtimes, scores)):\n",
        "            ax2.scatter(runtime, score, s=100, c=colors[i], alpha=0.7, label=alg)\n",
        "        \n",
        "        ax2.set_xlabel('Runtime (seconds)', fontsize=12)\n",
        "        ax2.set_ylabel('Best Score', fontsize=12)\n",
        "        ax2.set_title('Score vs Runtime Trade-off', fontsize=14, fontweight='bold')\n",
        "        ax2.legend()\n",
        "        ax2.grid(True, alpha=0.3)\n",
        "        \n",
        "        plt.tight_layout()\n",
        "        plt.show()\n",
        "\n",
        "print(\"✅ Evaluation and visualization framework implemented\")"
      ],
      "metadata": {
        "id": "evaluation_viz"
      },
      "execution_count": null,
      "outputs": []
    }
  ]
}
