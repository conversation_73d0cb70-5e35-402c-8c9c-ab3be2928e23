{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}}, "cells": [{"cell_type": "markdown", "source": ["Load datasets"], "metadata": {"id": "w2PKekZ3Cdbz"}}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "eB0Ydp549zyT"}, "outputs": [], "source": ["healthy_path = \"/content/psen1_healthy_combined.fasta\"\n", "unhealthy_path = \"/content/psen1_unhealthy_combined.fasta\"\n"]}, {"cell_type": "markdown", "source": ["Preprocessing + k-mer enrichment (+ optional ML check)"], "metadata": {"id": "akVRSIaH_6HN"}}, {"cell_type": "code", "source": ["import os, random, re\n", "import numpy as np\n", "import pandas as pd\n", "from collections import Counter\n", "import matplotlib.pyplot as plt\n", "plt.rcParams[\"figure.figsize\"] = (9,5)\n", "\n", "# -----------------------------\n", "# 1) FASTA reading + cleaning\n", "# -----------------------------\n", "def read_fasta(path):\n", "    seqs, headers = [], []\n", "    with open(path) as f:\n", "        seq, header = [], None\n", "        for line in f:\n", "            line = line.strip()\n", "            if not line:\n", "                continue\n", "            if line.startswith(\">\"):\n", "                if header:\n", "                    seqs.append(\"\".join(seq).upper())\n", "                    headers.append(header)\n", "                header = line[1:].strip()\n", "                seq = []\n", "            else:\n", "                seq.append(line)\n", "        if header:\n", "            seqs.append(\"\".join(seq).upper())\n", "            headers.append(header)\n", "    return headers, seqs\n", "\n", "def clean_sequence(s):\n", "    s = s.upper().replace(\"U\",\"T\")        # just in case RNA\n", "    s = re.sub(r\"[^ACGTN]\", \"N\", s)      # map unknowns to N\n", "    return s\n", "\n", "def preprocess_fasta(path, min_len=30):\n", "    headers, seqs = read_fasta(path)\n", "    seqs = [clean_sequence(s) for s in seqs]\n", "    # drop very short sequences\n", "    keep_idx = [i for i,s in enumerate(seqs) if len(s) >= min_len]\n", "    seqs = [seqs[i] for i in keep_idx]\n", "    headers = [headers[i] for i in keep_idx]\n", "    return headers, seqs\n", "\n", "h_headers, h_seqs = preprocess_fasta(healthy_path)\n", "u_headers, u_seqs = preprocess_fasta(unhealthy_path)\n", "\n", "print(f\"Loaded PSEN1: {len(h_seqs)} healthy, {len(u_seqs)} unhealthy sequences.\")\n", "\n", "# -----------------------------\n", "# 2) QC stats + plots\n", "# -----------------------------\n", "def seq_stats(seqs):\n", "    lengths = [len(s) for s in seqs]\n", "    gc_perc = [100*(s.count(\"G\")+s.count(\"C\"))/len(s) for s in seqs]\n", "    n_frac  = [100*s.count(\"N\")/len(s) for s in seqs]\n", "    return pd.DataFrame({\"length\": lengths, \"gc_percent\": gc_perc, \"N_percent\": n_frac})\n", "\n", "h_df = seq_stats(h_seqs); u_df = seq_stats(u_seqs)\n", "display(h_df.describe().T.round(3))\n", "display(u_df.describe().T.round(3))\n", "\n", "# length hist\n", "plt.figure()\n", "plt.hist(h_df[\"length\"], bins=20, alpha=0.7, label=\"Healthy\")\n", "plt.hist(u_df[\"length\"], bins=20, alpha=0.7, label=\"Unhealthy\")\n", "plt.title(\"PSEN1 length distribution\"); plt.xlabel(\"bp\"); plt.ylabel(\"count\"); plt.legend(); plt.show()\n", "\n", "# GC% hist\n", "plt.figure()\n", "plt.hist(h_df[\"gc_percent\"], bins=20, alpha=0.7, label=\"Healthy\")\n", "plt.hist(u_df[\"gc_percent\"], bins=20, alpha=0.7, label=\"Unhealthy\")\n", "plt.title(\"PSEN1 GC% distribution\"); plt.xlabel(\"GC%\"); plt.ylabel(\"count\"); plt.legend(); plt.show()\n", "\n", "# -----------------------------\n", "# 3) k-mer enrichment (baseline motifs)\n", "# -----------------------------\n", "def count_kmers(seqs, k=6):\n", "    counts = Counter()\n", "    for s in seqs:\n", "        for i in range(len(s)-k+1):\n", "            kmer = s[i:i+k]\n", "            if set(kmer) <= set(\"ACGT\"):  # ignore Ns\n", "                counts[kmer] += 1\n", "    return counts\n", "\n", "def enrichment(h_seqs, u_seqs, k=6, top=30):\n", "    h_counts, u_counts = count_kmers(h_seqs, k), count_kmers(u_seqs, k)\n", "    h_total, u_total = sum(h_counts.values()), sum(u_counts.values())\n", "    V = 4**k  # <PERSON><PERSON> smoothing\n", "    rows = []\n", "    for kmer in set(h_counts) | set(u_counts):\n", "        h_c, u_c = h_counts.get(kmer,0), u_counts.get(kmer,0)\n", "        p_h = (h_c+1)/(h_total+V)\n", "        p_u = (u_c+1)/(u_total+V)\n", "        rows.append({\"kmer\":kmer,\"h_count\":h_c,\"u_count\":u_c,\n", "                     \"log2fc_u_over_h\": np.log2(p_u/p_h),\n", "                     \"log2fc_h_over_u\": np.log2(p_h/p_u)})\n", "    df = pd.DataFrame(rows)\n", "    top_unhealthy = df.sort_values(\"log2fc_u_over_h\", ascending=False).head(top).reset_index(drop=True)\n", "    top_healthy   = df.sort_values(\"log2fc_h_over_u\", ascending=False).head(top).reset_index(drop=True)\n", "    return top_unhealthy, top_healthy, df\n", "\n", "K = 6\n", "top_u, top_h, enr_all = enrichment(h_seqs, u_seqs, k=K, top=30)\n", "print(\"Top unhealthy-enriched kmers:\"); display(top_u.head(10))\n", "print(\"Top healthy-enriched kmers:\");   display(top_h.head(10))\n", "\n", "# Save to drive (optional)\n", "top_u.to_csv(\"/content/psen1_top6mers_unhealthy.csv\", index=False)\n", "top_h.to_csv(\"/content/psen1_top6mers_healthy.csv\", index=False)\n", "enr_all.to_csv(\"/content/psen1_6mer_enrichment_full.csv\", index=False)\n", "\n", "# -----------------------------\n", "# 4) Quick ML validation (optional)\n", "# -----------------------------\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import accuracy_score, roc_auc_score, roc_curve\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "\n", "TOP_N = 20\n", "motifs = top_u.kmer.head(TOP_N).tolist() + top_h.kmer.head(TOP_N).tolist()\n", "\n", "def motif_features(seqs, motifs):\n", "    X = np.zeros((len(seqs), len(motifs)*2))\n", "    for i, s in enumerate(seqs):\n", "        L = len(s)\n", "        for j, m in enumerate(motifs):\n", "            c = s.count(m)\n", "            X[i, j] = 1 if c>0 else 0\n", "            X[i, j+len(motifs)] = c/max(1,L)\n", "    cols = [f\"pres_{m}\" for m in motifs] + [f\"freq_{m}\" for m in motifs]\n", "    return pd.DataFrame(X, columns=cols)\n", "\n", "X_h = motif_features(h_seqs, motifs)\n", "X_u = motif_features(u_seqs, motifs)\n", "X = pd.concat([X_h, X_u], axis=0).values\n", "y = np.array([0]*len(X_h) + [1]*len(X_u))\n", "\n", "Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.25, stratify=y, random_state=42)\n", "\n", "lr = LogisticRegression(max_iter=2000).fit(Xtr, ytr)\n", "rf = RandomForestClassifier(n_estimators=300, random_state=42).fit(Xtr, ytr)\n", "\n", "from math import isfinite\n", "def safe_auc(model):\n", "    try:\n", "        p = model.predict_proba(Xte)[:,1]\n", "        return roc_auc_score(yte, p)\n", "    except Exception:\n", "        return float(\"nan\")\n", "\n", "print(f\"LogReg acc/auc: {lr.score(Xte,yte):.3f} / {safe_auc(lr):.3f}\")\n", "print(f\"RandForest acc/auc: {rf.score(Xte,yte):.3f} / {safe_auc(rf):.3f}\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "id": "uNykwCT2-8yW", "outputId": "bf73bbe0-28a7-4130-b0e6-afe496735efb"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Loaded PSEN1: 10 healthy, 26 unhealthy sequences.\n"]}, {"output_type": "display_data", "data": {"text/plain": ["            count       mean    std        min        25%        50%  \\\n", "length       10.0  87274.800  2.394  87269.000  87275.000  87275.000   \n", "gc_percent   10.0     42.727  0.001     42.727     42.727     42.727   \n", "N_percent    10.0      0.000  0.000      0.000      0.000      0.000   \n", "\n", "                  75%        max  \n", "length      87275.000  87279.000  \n", "gc_percent     42.728     42.729  \n", "N_percent       0.000      0.000  "], "text/html": ["\n", "  <div id=\"df-a3d92395-f9ca-40b5-af7d-4a96d385e5a1\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>length</th>\n", "      <td>10.0</td>\n", "      <td>87274.800</td>\n", "      <td>2.394</td>\n", "      <td>87269.000</td>\n", "      <td>87275.000</td>\n", "      <td>87275.000</td>\n", "      <td>87275.000</td>\n", "      <td>87279.000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gc_percent</th>\n", "      <td>10.0</td>\n", "      <td>42.727</td>\n", "      <td>0.001</td>\n", "      <td>42.727</td>\n", "      <td>42.727</td>\n", "      <td>42.727</td>\n", "      <td>42.728</td>\n", "      <td>42.729</td>\n", "    </tr>\n", "    <tr>\n", "      <th>N_percent</th>\n", "      <td>10.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-a3d92395-f9ca-40b5-af7d-4a96d385e5a1')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-a3d92395-f9ca-40b5-af7d-4a96d385e5a1 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-a3d92395-f9ca-40b5-af7d-4a96d385e5a1');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-42eec008-69a4-41b4-93a4-a4124a70c61c\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-42eec008-69a4-41b4-93a4-a4124a70c61c')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-42eec008-69a4-41b4-93a4-a4124a70c61c button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"print(f\\\"RandForest acc/auc: {rf\",\n  \"rows\": 3,\n  \"fields\": [\n    {\n      \"column\": \"count\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0,\n        \"min\": 10.0,\n        \"max\": 10.0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          10.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"mean\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 50375.79958093942,\n        \"min\": 0.0,\n        \"max\": 87274.8,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          87274.8\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"std\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1.381887959761331,\n        \"min\": 0.0,\n        \"max\": 2.394,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          2.394\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"min\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 50372.45094967926,\n        \"min\": 0.0,\n        \"max\": 87269.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          87269.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"25%\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 50375.915050982876,\n        \"min\": 0.0,\n        \"max\": 87275.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          87275.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"50%\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 50375.915050982876,\n        \"min\": 0.0,\n        \"max\": 87275.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          87275.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"75%\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 50375.91476251982,\n        \"min\": 0.0,\n        \"max\": 87275.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          87275.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"max\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 50378.22387492583,\n        \"min\": 0.0,\n        \"max\": 87279.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          87279.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["            count      mean       std       min       25%       50%     75%  \\\n", "length       26.0  4469.846  2359.047  1000.000  1000.000  6006.000  6018.0   \n", "gc_percent   26.0    44.599     2.062    43.407    43.407    43.436    45.0   \n", "N_percent    26.0     0.000     0.000     0.000     0.000     0.000     0.0   \n", "\n", "               max  \n", "length      6018.0  \n", "gc_percent    49.7  \n", "N_percent      0.0  "], "text/html": ["\n", "  <div id=\"df-bd0baefc-c065-4664-9195-84570b8d24dc\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>length</th>\n", "      <td>26.0</td>\n", "      <td>4469.846</td>\n", "      <td>2359.047</td>\n", "      <td>1000.000</td>\n", "      <td>1000.000</td>\n", "      <td>6006.000</td>\n", "      <td>6018.0</td>\n", "      <td>6018.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>gc_percent</th>\n", "      <td>26.0</td>\n", "      <td>44.599</td>\n", "      <td>2.062</td>\n", "      <td>43.407</td>\n", "      <td>43.407</td>\n", "      <td>43.436</td>\n", "      <td>45.0</td>\n", "      <td>49.7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>N_percent</th>\n", "      <td>26.0</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.000</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-bd0baefc-c065-4664-9195-84570b8d24dc')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-bd0baefc-c065-4664-9195-84570b8d24dc button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-bd0baefc-c065-4664-9195-84570b8d24dc');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-cb1b433c-1eb8-42d7-8f95-a042ecae1c62\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-cb1b433c-1eb8-42d7-8f95-a042ecae1c62')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-cb1b433c-1eb8-42d7-8f95-a042ecae1c62 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"print(f\\\"RandForest acc/auc: {rf\",\n  \"rows\": 3,\n  \"fields\": [\n    {\n      \"column\": \"count\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.0,\n        \"min\": 26.0,\n        \"max\": 26.0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          26.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"mean\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 2567.8889950543035,\n        \"min\": 0.0,\n        \"max\": 4469.846,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          4469.846\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"std\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 1361.4015627456629,\n        \"min\": 0.0,\n        \"max\": 2359.047,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          2359.047\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"min\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 565.2365780948128,\n        \"min\": 0.0,\n        \"max\": 1000.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          1000.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"25%\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 565.2365780948128,\n        \"min\": 0.0,\n        \"max\": 1000.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          1000.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"50%\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3455.095081667845,\n        \"min\": 0.0,\n        \"max\": 6006.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          6006.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"75%\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3461.576663891759,\n        \"min\": 0.0,\n        \"max\": 6018.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          6018.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"max\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 3460.2359982136095,\n        \"min\": 0.0,\n        \"max\": 6018.0,\n        \"num_unique_values\": 3,\n        \"samples\": [\n          6018.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 900x500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 900x500 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Top unhealthy-enriched kmers:\n"]}, {"output_type": "display_data", "data": {"text/plain": ["     kmer  h_count  u_count  log2fc_u_over_h  log2fc_h_over_u\n", "0  TTCGAC       10       26         4.162473        -4.162473\n", "1  GTCCGA       10       24         4.051442        -4.051442\n", "2  CGACCC       10       23         3.992548        -3.992548\n", "3  CGGTCC       10       23         3.992548        -3.992548\n", "4  GCGGTC       10       23         3.992548        -3.992548\n", "5  GTCCAC       40       87         3.968897        -3.968897\n", "6  CATCGC       20       41         3.867017        -3.867017\n", "7  TCGTAG       10       21         3.867017        -3.867017\n", "8  ACGCTT       10       21         3.867017        -3.867017\n", "9  TCATCG       30       59         3.819711        -3.819711"], "text/html": ["\n", "  <div id=\"df-45962271-df0c-4f00-9aae-8b5fdb2d97d7\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>kmer</th>\n", "      <th>h_count</th>\n", "      <th>u_count</th>\n", "      <th>log2fc_u_over_h</th>\n", "      <th>log2fc_h_over_u</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TTCGAC</td>\n", "      <td>10</td>\n", "      <td>26</td>\n", "      <td>4.162473</td>\n", "      <td>-4.162473</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>GTCCGA</td>\n", "      <td>10</td>\n", "      <td>24</td>\n", "      <td>4.051442</td>\n", "      <td>-4.051442</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CGACCC</td>\n", "      <td>10</td>\n", "      <td>23</td>\n", "      <td>3.992548</td>\n", "      <td>-3.992548</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>CGGTCC</td>\n", "      <td>10</td>\n", "      <td>23</td>\n", "      <td>3.992548</td>\n", "      <td>-3.992548</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GCGGTC</td>\n", "      <td>10</td>\n", "      <td>23</td>\n", "      <td>3.992548</td>\n", "      <td>-3.992548</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>GTCCAC</td>\n", "      <td>40</td>\n", "      <td>87</td>\n", "      <td>3.968897</td>\n", "      <td>-3.968897</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>CATCGC</td>\n", "      <td>20</td>\n", "      <td>41</td>\n", "      <td>3.867017</td>\n", "      <td>-3.867017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>TCGTAG</td>\n", "      <td>10</td>\n", "      <td>21</td>\n", "      <td>3.867017</td>\n", "      <td>-3.867017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>ACGCTT</td>\n", "      <td>10</td>\n", "      <td>21</td>\n", "      <td>3.867017</td>\n", "      <td>-3.867017</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>TCATCG</td>\n", "      <td>30</td>\n", "      <td>59</td>\n", "      <td>3.819711</td>\n", "      <td>-3.819711</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-45962271-df0c-4f00-9aae-8b5fdb2d97d7')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-45962271-df0c-4f00-9aae-8b5fdb2d97d7 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-45962271-df0c-4f00-9aae-8b5fdb2d97d7');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-48b4fdff-b1ef-4783-bd57-4d9a2e1f2b97\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-48b4fdff-b1ef-4783-bd57-4d9a2e1f2b97')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-48b4fdff-b1ef-4783-bd57-4d9a2e1f2b97 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"print(f\\\"RandForest acc/auc: {rf\",\n  \"rows\": 10,\n  \"fields\": [\n    {\n      \"column\": \"kmer\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"ACGCTT\",\n          \"GTCCGA\",\n          \"GTCCAC\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"h_count\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 10,\n        \"min\": 10,\n        \"max\": 40,\n        \"num_unique_values\": 4,\n        \"samples\": [\n          40,\n          30,\n          10\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"u_count\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 21,\n        \"min\": 21,\n        \"max\": 87,\n        \"num_unique_values\": 7,\n        \"samples\": [\n          26,\n          24,\n          21\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"log2fc_u_over_h\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.10445479158935164,\n        \"min\": 3.819711375340729,\n        \"max\": 4.162472973645257,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          4.162472973645257,\n          4.051441661256513,\n          3.819711375340729\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"log2fc_h_over_u\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.10445479158935171,\n        \"min\": -4.162472973645257,\n        \"max\": -3.8197113753407295,\n        \"num_unique_values\": 6,\n        \"samples\": [\n          -4.162472973645257,\n          -4.051441661256513,\n          -3.8197113753407295\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Top healthy-enriched kmers:\n"]}, {"output_type": "display_data", "data": {"text/plain": ["     kmer  h_count  u_count  log2fc_u_over_h  log2fc_h_over_u\n", "0  AGGCTG     1400        0        -7.585224         7.585224\n", "1  CCCAGC      890        0        -6.932265         6.932265\n", "2  ATATAT      820        0        -6.814221         6.814221\n", "3  TATATA      810        0        -6.796541         6.796541\n", "4  GGAGGC      760        0        -6.704736         6.704736\n", "5  TGTAAT      750        0        -6.685652         6.685652\n", "6  TTACAG      730        0        -6.646711         6.646711\n", "7  GCCAGG      720        0        -6.626838         6.626838\n", "8  GGATTA      660        0        -6.501489         6.501489\n", "9  CTGTAA      650        0        -6.479497         6.479497"], "text/html": ["\n", "  <div id=\"df-8a9ae9ab-26d5-40bd-877e-168f20688d1c\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>kmer</th>\n", "      <th>h_count</th>\n", "      <th>u_count</th>\n", "      <th>log2fc_u_over_h</th>\n", "      <th>log2fc_h_over_u</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>AGGCTG</td>\n", "      <td>1400</td>\n", "      <td>0</td>\n", "      <td>-7.585224</td>\n", "      <td>7.585224</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CCCAGC</td>\n", "      <td>890</td>\n", "      <td>0</td>\n", "      <td>-6.932265</td>\n", "      <td>6.932265</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ATATAT</td>\n", "      <td>820</td>\n", "      <td>0</td>\n", "      <td>-6.814221</td>\n", "      <td>6.814221</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>TATATA</td>\n", "      <td>810</td>\n", "      <td>0</td>\n", "      <td>-6.796541</td>\n", "      <td>6.796541</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>GGAGGC</td>\n", "      <td>760</td>\n", "      <td>0</td>\n", "      <td>-6.704736</td>\n", "      <td>6.704736</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>TGTAAT</td>\n", "      <td>750</td>\n", "      <td>0</td>\n", "      <td>-6.685652</td>\n", "      <td>6.685652</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>TTACAG</td>\n", "      <td>730</td>\n", "      <td>0</td>\n", "      <td>-6.646711</td>\n", "      <td>6.646711</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>GCCAGG</td>\n", "      <td>720</td>\n", "      <td>0</td>\n", "      <td>-6.626838</td>\n", "      <td>6.626838</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>GGATTA</td>\n", "      <td>660</td>\n", "      <td>0</td>\n", "      <td>-6.501489</td>\n", "      <td>6.501489</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>CTGTAA</td>\n", "      <td>650</td>\n", "      <td>0</td>\n", "      <td>-6.479497</td>\n", "      <td>6.479497</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-8a9ae9ab-26d5-40bd-877e-168f20688d1c')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-8a9ae9ab-26d5-40bd-877e-168f20688d1c button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-8a9ae9ab-26d5-40bd-877e-168f20688d1c');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-95a3b06b-c22a-4b26-8c23-11b8fd3fd0dc\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-95a3b06b-c22a-4b26-8c23-11b8fd3fd0dc')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-95a3b06b-c22a-4b26-8c23-11b8fd3fd0dc button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"print(f\\\"RandForest acc/auc: {rf\",\n  \"rows\": 10,\n  \"fields\": [\n    {\n      \"column\": \"kmer\",\n      \"properties\": {\n        \"dtype\": \"string\",\n        \"num_unique_values\": 10,\n        \"samples\": [\n          \"GGATTA\",\n          \"CCCAGC\",\n          \"TGTAAT\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"h_count\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 216,\n        \"min\": 650,\n        \"max\": 1400,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          660,\n          890,\n          750\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"u_count\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0,\n        \"min\": 0,\n        \"max\": 0,\n        \"num_unique_values\": 1,\n        \"samples\": [\n          0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"log2fc_u_over_h\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3156306655551323,\n        \"min\": -7.585224150311732,\n        \"max\": -6.4794966430465495,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          -6.5014893713886055\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"log2fc_h_over_u\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.3156306655551323,\n        \"min\": 6.4794966430465495,\n        \"max\": 7.585224150311732,\n        \"num_unique_values\": 10,\n        \"samples\": [\n          6.5014893713886055\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["LogReg acc/auc: 1.000 / 1.000\n", "RandForest acc/auc: 1.000 / 1.000\n"]}]}, {"cell_type": "markdown", "source": ["GA / PSO / Hybrid (uses the same h_seqs, u_seqs)"], "metadata": {"id": "y2UwwP9pAOT-"}}, {"cell_type": "code", "source": ["import numpy as np, random\n", "\n", "# ---- Fitness (reuses sequences from cell 1) ----\n", "def motif_fitness(motif, healthy_seqs, unhealthy_seqs):\n", "    if not motif: return -1e9\n", "    k = len(motif)\n", "    h_hits = sum(s.count(motif) for s in healthy_seqs)\n", "    u_hits = sum(s.count(motif) for s in unhealthy_seqs)\n", "    if h_hits + u_hits == 0:\n", "        return -10.0  # discourage dead motifs\n", "    p_h = (h_hits+1)/(len(healthy_seqs)+1)\n", "    p_u = (u_hits+1)/(len(unhealthy_seqs)+1)\n", "    enrichment = np.log2(p_u/p_h)\n", "    # base-composition info term to avoid low-complexity strings\n", "    counts = np.array([motif.count(b) for b in \"ACGT\"])+1\n", "    probs = counts/counts.sum()\n", "    info = 2 + np.sum(probs*np.log2(probs))\n", "    return enrichment + 0.5*info\n", "\n", "# ---- GA ----\n", "def genetic_algorithm(h_seqs, u_seqs, k=6, pop=60, gens=60, mut=0.25, elitism=2):\n", "    bases=\"ACGT\"\n", "    popn=[''.join(random.choice(bases) for _ in range(k)) for _ in range(pop)]\n", "    for g in range(gens):\n", "        fit = np.array([motif_fitness(m,h_seqs,u_seqs) for m in popn])\n", "        elite_idx = fit.argsort()[-elitism:][::-1]\n", "        elites = [popn[i] for i in elite_idx]\n", "        probs = (fit - fit.min() + 1e-6); probs = probs/probs.sum()\n", "        parents = np.random.choice(popn, size=pop-elitism, p=probs)\n", "        kids=[]\n", "        for i in range(0, len(parents), 2):\n", "            p1 = parents[i]\n", "            p2 = parents[(i+1) % len(parents)]\n", "            cx = random.randint(1, k-1)\n", "            c1, c2 = p1[:cx]+p2[cx:], p2[:cx]+p1[cx:]\n", "            kids += [c1, c2]\n", "        # mutation\n", "        for i in range(len(kids)):\n", "            if random.random() < mut:\n", "                pos = random.randint(0, k-1)\n", "                b = random.choice(\"ACGT\")\n", "                kids[i] = kids[i][:pos] + b + kids[i][pos+1:]\n", "        popn = elites + kids[:pop-elitism]\n", "    fit = np.array([motif_fitness(m,h_seqs,u_seqs) for m in popn])\n", "    idx = fit.argmax()\n", "    return popn[idx], float(fit[idx])\n", "\n", "# ---- PSO (bare-bones style on discrete strings) ----\n", "def pso(h_seqs, u_seqs, k=6, swarm=40, iters=50, mut_prob=0.3):\n", "    bases=\"ACGT\"\n", "    swarm=[''.join(random.choice(bases) for _ in range(k)) for _ in range(swarm)]\n", "    pbest=swarm[:]\n", "    pscore=[motif_fitness(m,h_seqs,u_seqs) for m in swarm]\n", "    gbest = pbest[int(np.argmax(pscore))]\n", "    gscore= max(pscore)\n", "    for t in range(iters):\n", "        for i,particle in enumerate(swarm):\n", "            chars=list(particle)\n", "            for pos in range(k):\n", "                if random.random()<mut_prob:\n", "                    # move toward gbest at half the time\n", "                    if random.random()<0.5:\n", "                        chars[pos]=gbest[pos]\n", "                    else:\n", "                        chars[pos]=random.choice(bases)\n", "            swarm[i]=''.join(chars)\n", "            sc = motif_fitness(swarm[i], h_seqs, u_seqs)\n", "            if sc>pscore[i]:\n", "                pbest[i], pscore[i] = swarm[i], sc\n", "            if sc>gscore:\n", "                gbest, gscore = swarm[i], sc\n", "    return gbest, gscore\n", "\n", "# ---- Hybrid ----\n", "def hybrid_ga_pso(h_seqs, u_seqs, k=6, rounds=3):\n", "    best_m, best_s = None, -1e9\n", "    for r in range(rounds):\n", "        m1, s1 = pso(h_seqs, u_seqs, k=k, swarm=40, iters=50)\n", "        # seed GA population with gbest + randoms\n", "        gm, gs = genetic_algorithm(h_seqs, u_seqs, k=k, pop=60, gens=60, mut=0.25, elitism=4)\n", "        if gs > best_s:\n", "            best_m, best_s = gm, gs\n", "        print(f\"Round {r+1}: PSO seed {m1} -> GA best {gm} (score={gs:.3f})\")\n", "    return best_m, best_s\n", "\n", "# ---- Run discovery ----\n", "ga_motif, ga_score = genetic_algorithm(h_seqs, u_seqs, k=6)\n", "print(\"GA ->\", ga_motif, ga_score)\n", "\n", "pso_motif, pso_score = pso(h_seqs, u_seqs, k=6)\n", "print(\"PSO ->\", pso_motif, pso_score)\n", "\n", "hybrid_motif, hybrid_score = hybrid_ga_pso(h_seqs, u_seqs, k=6, rounds=3)\n", "print(\"Hybrid ->\", hybrid_motif, hybrid_score)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Xsv2H5LzAJxB", "outputId": "283952e9-1a87-45f9-d900-0665f0b1ac32"}, "execution_count": 6, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["GA -> TCATCG -0.32823689553186236\n", "PSO -> GTCCGA -0.09650660961607813\n", "Round 1: PSO seed GTCCGA -> GA best TTCGAC (score=0.015)\n", "Round 2: PSO seed GTCCGA -> GA best TTCGAC (score=0.015)\n", "Round 3: PSO seed CGACCC -> GA best GCGGTC (score=-0.093)\n", "Hybrid -> TTCGAC 0.014524702772665599\n"]}]}, {"cell_type": "markdown", "source": ["Build PWM + Sequence Logo"], "metadata": {"id": "Q0Zg1TWQAMi8"}}, {"cell_type": "code", "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# -----------------------------\n", "# 1) PWM from motif hits\n", "# -----------------------------\n", "def build_pwm(seqs, motif, flank=0):\n", "    \"\"\"Build PWM from all exact motif hits.\"\"\"\n", "    k = len(motif)\n", "    hits = []\n", "    for s in seqs:\n", "        for i in range(len(s)-k+1):\n", "            if s[i:i+k] == motif:\n", "                start = max(0, i-flank)\n", "                end = min(len(s), i+k+flank)\n", "                hits.append(s[start:end])\n", "    if not hits:\n", "        return None, []\n", "    L = len(hits[0])\n", "    pwm = np.ones((L, 4))  # pseudocounts\n", "    idx = {'A':0,'C':1,'G':2,'T':3}\n", "    for h in hits:\n", "        for j,ch in enumerate(h):\n", "            if ch in idx:\n", "                pwm[j, idx[ch]] += 1\n", "    pwm = pwm / pwm.sum(axis=1, keepdims=True)\n", "    return pwm, hits\n", "\n", "# -----------------------------\n", "# 2) Plot logo-like bar chart\n", "# -----------------------------\n", "def plot_pwm_logo(pwm, motif):\n", "    bases = \"ACGT\"\n", "    x = np.arange(pwm.shape[0])\n", "    plt.figure(figsize=(10,3))\n", "    bottom = np.zeros(pwm.shape[0])\n", "    for j, base in enumerate(bases):\n", "        plt.bar(x, pwm[:, j], bottom=bottom, label=base, alpha=0.7)\n", "        bottom += pwm[:, j]\n", "    plt.xticks(range(len(motif)), list(motif))\n", "    plt.title(f\"Sequence Logo for motif {motif}\")\n", "    plt.xlabel(\"Position\")\n", "    plt.ylabel(\"Probability\")\n", "    plt.legend()\n", "    plt.show()\n", "\n", "# Example: use your best hybrid motif\n", "motif = hybrid_motif\n", "pwm, hits = build_pwm(u_seqs, motif)\n", "\n", "if pwm is not None:\n", "    pwm_df = pd.DataFrame(pwm, columns=list(\"ACGT\"))\n", "    print(\"PWM for motif\", motif)\n", "    display(pwm_df.round(3))\n", "    plot_pwm_logo(pwm, motif)\n", "    print(\"Number of hits found in unhealthy sequences:\", len(hits))\n", "else:\n", "    print(\"No exact hits found for motif.\")\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 588}, "id": "jYyObxkGBynp", "outputId": "b93ad2ec-0fab-4e12-8f3b-35c0f0ea3b4c"}, "execution_count": 11, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["PWM for motif TTCGAC\n"]}, {"output_type": "display_data", "data": {"text/plain": ["       A      C      G      T\n", "0  0.033  0.033  0.033  0.900\n", "1  0.033  0.033  0.033  0.900\n", "2  0.033  0.900  0.033  0.033\n", "3  0.033  0.033  0.900  0.033\n", "4  0.900  0.033  0.033  0.033\n", "5  0.033  0.900  0.033  0.033"], "text/html": ["\n", "  <div id=\"df-7b582c66-b2df-4a66-8732-788a2b6ba41f\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>C</th>\n", "      <th>G</th>\n", "      <th>T</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "      <td>0.900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "      <td>0.900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.033</td>\n", "      <td>0.900</td>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "      <td>0.900</td>\n", "      <td>0.033</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.900</td>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.033</td>\n", "      <td>0.900</td>\n", "      <td>0.033</td>\n", "      <td>0.033</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-7b582c66-b2df-4a66-8732-788a2b6ba41f')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-7b582c66-b2df-4a66-8732-788a2b6ba41f button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-7b582c66-b2df-4a66-8732-788a2b6ba41f');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "    <div id=\"df-270c6add-c14e-400b-b62b-22e5ad911b15\">\n", "      <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-270c6add-c14e-400b-b62b-22e5ad911b15')\"\n", "                title=\"Suggest charts\"\n", "                style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "      </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "      <script>\n", "        async function quickchart(key) {\n", "          const quickchartButtonEl =\n", "            document.querySelector('#' + key + ' button');\n", "          quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "          quickchartButtonEl.classList.add('colab-df-spinner');\n", "          try {\n", "            const charts = await google.colab.kernel.invokeFunction(\n", "                'suggest<PERSON><PERSON>s', [key], {});\n", "          } catch (error) {\n", "            console.error('Error during call to suggest<PERSON>harts:', error);\n", "          }\n", "          quickchartButtonEl.classList.remove('colab-df-spinner');\n", "          quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "        }\n", "        (() => {\n", "          let quickchartButtonEl =\n", "            document.querySelector('#df-270c6add-c14e-400b-b62b-22e5ad911b15 button');\n", "          quickchartButtonEl.style.display =\n", "            google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "        })();\n", "      </script>\n", "    </div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "summary": "{\n  \"name\": \"    print(\\\"No exact hits found for motif\",\n  \"rows\": 6,\n  \"fields\": [\n    {\n      \"column\": \"A\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.35395126783216924,\n        \"min\": 0.033,\n        \"max\": 0.9,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0.9,\n          0.033\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"C\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.44771687482157746,\n        \"min\": 0.033,\n        \"max\": 0.9,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0.9,\n          0.033\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"G\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.35395126783216924,\n        \"min\": 0.033,\n        \"max\": 0.9,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0.9,\n          0.033\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"T\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 0.44771687482157735,\n        \"min\": 0.033,\n        \"max\": 0.9,\n        \"num_unique_values\": 2,\n        \"samples\": [\n          0.033,\n          0.9\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x300 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["Number of hits found in unhealthy sequences: 26\n"]}]}, {"cell_type": "markdown", "source": ["<PERSON><PERSON><PERSON> in Healthy vs. Unhealthy"], "metadata": {"id": "_xwfNr7sBJp2"}}, {"cell_type": "code", "source": ["def motif_scan(seqs, motif):\n", "    return sum(s.count(motif) for s in seqs)\n", "\n", "for m in [ga_motif, pso_motif, hybrid_motif]:\n", "    h_hits = motif_scan(h_seqs, m)\n", "    u_hits = motif_scan(u_seqs, m)\n", "    print(f\"Motif {m} -> Healthy hits={h_hits}, Unhealthy hits={u_hits}\")\n", "\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nu7u027NBMop", "outputId": "21975596-8ca6-492a-dca0-cf96dc7c648b"}, "execution_count": 12, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Motif TCATCG -> Healthy hits=30, Unhealthy hits=59\n", "Motif GTCCGA -> Healthy hits=10, Unhealthy hits=24\n", "Motif TTCGAC -> Healthy hits=10, Unhealthy hits=26\n"]}]}, {"cell_type": "markdown", "source": ["ML Validation using Discovered Motifs"], "metadata": {"id": "_67PcQMKBSLL"}}, {"cell_type": "code", "source": ["from sklearn.model_selection import train_test_split\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import accuracy_score, roc_auc_score\n", "\n", "# Use GA, PSO, Hybrid motifs as features\n", "motifs = [ga_motif, pso_motif, hybrid_motif]\n", "print(\"Using motifs as features:\", motifs)\n", "\n", "X_h = np.array([[s.count(m) for m in motifs] for s in h_seqs])\n", "X_u = np.array([[s.count(m) for m in motifs] for s in u_seqs])\n", "X = np.vstack([X_h, X_u])\n", "y = np.array([0]*len(X_h) + [1]*len(X_u))  # 0=healthy, 1=unhealthy\n", "\n", "# Train/test split\n", "Xtr, Xte, ytr, yte = train_test_split(X, y, test_size=0.25, stratify=y, random_state=42)\n", "\n", "# Logistic Regression\n", "lr = LogisticRegression(max_iter=2000).fit(Xtr, ytr)\n", "lr_pred = lr.predict(Xte)\n", "print(\"LogReg Accuracy:\", accuracy_score(yte, lr_pred), \"AUC:\", roc_auc_score(yte, lr.predict_proba(Xte)[:,1]))\n", "\n", "# Random Forest\n", "rf = RandomForestClassifier(n_estimators=300, random_state=42).fit(Xtr, ytr)\n", "rf_pred = rf.predict(Xte)\n", "print(\"RF Accuracy:\", accuracy_score(yte, rf_pred), \"AUC:\", roc_auc_score(yte, rf.predict_proba(Xte)[:,1]))\n", "print(\"Feature importances (motifs):\", dict(zip(motifs, rf.feature_importances_)))\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Qls5Lg-RBS4t", "outputId": "726bffa8-2564-4bde-c7ca-78b205b4c374"}, "execution_count": 13, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Using motifs as features: ['TCATCG', 'GTCCGA', 'TTCGAC']\n", "LogReg Accuracy: 0.6666666666666666 AUC: 0.6666666666666666\n", "RF Accuracy: 0.6666666666666666 AUC: 0.6666666666666666\n", "Feature importances (motifs): {'TCATCG': np.float64(0.8386508532501932), 'GTCCGA': np.float64(0.16134914674980688), 'TTCGAC': np.float64(0.0)}\n"]}]}, {"cell_type": "markdown", "source": ["Save Results"], "metadata": {"id": "6yn8igZECNUC"}}, {"cell_type": "code", "source": ["pd.DataFrame({\"GA\":[ga_motif], \"PSO\":[pso_motif], \"Hybrid\":[hybrid_motif]}).to_csv(\"/content/psen1_discovered_motifs.csv\", index=False)\n", "if pwm is not None:\n", "    pd.DataFrame(pwm, columns=list(\"ACGT\")).to_csv(f\"/content/psen1_pwm_{motif}.csv\", index=False)\n", "print(\"Saved motifs and PWM to /content/\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "r43wQB6HCQbf", "outputId": "5a04fada-34cd-482f-cf6c-724b1664be5e"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Saved motifs and PWM to /content/\n"]}]}]}