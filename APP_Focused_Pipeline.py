# APP Gene Focused Pipeline - Streamlined Implementation
# Focus: APP gene only, ElitistGA + InertiaWeightPSO + Sequential Hybrid

import numpy as np
import matplotlib.pyplot as plt
import time
import random
import copy
from dataclasses import dataclass
from typing import List, Optional

print("🧬 APP Gene Focused Motif Discovery Pipeline")
print("="*60)
print("📊 Focus: APP gene sequences only")
print("🔬 Algorithms: ElitistGA + InertiaWeightPSO + Sequential Hybrid")
print("🎯 Goal: Complete pipeline for publication")

# Cell 1: Load APP Gene Data Only
def load_app_sequences():
    """Load only APP gene sequences"""
    
    print("\n📂 Loading APP Gene Sequences...")
    
    # Focus on APP gene only
    data_files = [
        ('APP_healthy_combined.fasta', 0),
        ('app_unhealthy_combined.fasta', 1)
    ]
    
    sequences = []
    labels = []
    
    for filename, label in data_files:
        try:
            print(f"   📄 Loading {filename}...")
            with open(filename, 'r') as file:
                content = file.read()
                
            # Parse FASTA format
            current_seq = ""
            for line in content.split('\n'):
                if line.startswith('>'):
                    if current_seq:
                        sequences.append(current_seq.upper())
                        labels.append(label)
                        current_seq = ""
                else:
                    current_seq += line.strip()
            
            # Add last sequence
            if current_seq:
                sequences.append(current_seq.upper())
                labels.append(label)
                
            print(f"   ✅ Loaded {sum(1 for l in labels if l == label)} sequences")
            
        except FileNotFoundError:
            print(f"   ❌ File {filename} not found")
            continue
    
    print(f"\n📊 APP Dataset Summary:")
    print(f"   Total sequences: {len(sequences)}")
    print(f"   Healthy (APP): {sum(1 for l in labels if l == 0)}")
    print(f"   Unhealthy (APP): {sum(1 for l in labels if l == 1)}")
    print(f"   Average length: {np.mean([len(s) for s in sequences]):.0f} bp")
    print(f"   Length range: {min(len(s) for s in sequences)} - {max(len(s) for s in sequences)} bp")
    
    return sequences, labels

# Load APP sequences
sequences, labels = load_app_sequences()

if len(sequences) == 0:
    print("❌ No sequences loaded. Please check file paths.")
else:
    print("✅ APP sequences loaded successfully!")

# Cell 2: Core Algorithm Classes (Simplified)
@dataclass
class MotifResult:
    motif: str
    score: float
    positions: List[int]
    pwm: np.ndarray
    information_content: float
    algorithm: str
    runtime: float
    convergence_history: List[float]

class BaseMotifDiscovery:
    """Base class for motif discovery algorithms"""
    
    def __init__(self, sequences: List[str], motif_length: int, config: dict):
        self.sequences = sequences
        self.motif_length = motif_length
        self.config = config
    
    def calculate_pwm(self, motif_instances: List[str]) -> np.ndarray:
        """Calculate Position Weight Matrix"""
        if not motif_instances:
            return np.ones((4, self.motif_length)) * 0.25
        
        pwm = np.zeros((4, self.motif_length))
        nucleotide_to_index = {'A': 0, 'T': 1, 'G': 2, 'C': 3}
        
        for instance in motif_instances:
            for pos, nuc in enumerate(instance):
                if pos < self.motif_length and nuc in nucleotide_to_index:
                    pwm[nucleotide_to_index[nuc], pos] += 1
        
        # Add pseudocounts and normalize
        pwm += 0.01
        pwm = pwm / np.sum(pwm, axis=0)
        
        return pwm
    
    def calculate_information_content(self, pwm: np.ndarray) -> float:
        """Calculate information content of PWM"""
        ic = 0.0
        for pos in range(pwm.shape[1]):
            for nuc in range(4):
                if pwm[nuc, pos] > 0:
                    ic += pwm[nuc, pos] * np.log2(pwm[nuc, pos] / 0.25)
        return ic
    
    def score_motif(self, motif: str, sequences: List[str]) -> float:
        """Score a motif against sequences"""
        if not motif:
            return 0.0
        
        total_score = 0.0
        for seq in sequences:
            best_score = 0.0
            for i in range(len(seq) - len(motif) + 1):
                subseq = seq[i:i + len(motif)]
                matches = sum(1 for a, b in zip(motif, subseq) if a == b)
                score = matches / len(motif)
                best_score = max(best_score, score)
            total_score += best_score
        
        return total_score / len(sequences)

print("✅ Core algorithm classes defined")

# Cell 3: ElitistGA Implementation (Simplified)
class Candidate:
    def __init__(self, positions: List[int]):
        self.positions = positions
        self.fitness = 0.0

class ElitistGA(BaseMotifDiscovery):
    """Elitist Genetic Algorithm - Simplified Version"""
    
    def discover_motifs(self) -> MotifResult:
        start_time = time.time()
        population_size = self.config.get('population_size', 20)
        max_generations = self.config.get('max_generations', 30)
        mutation_rate = self.config.get('mutation_rate', 0.1)
        elite_size = max(1, population_size // 10)
        convergence_history = []
        
        print(f"   🧬 ElitistGA: {population_size} individuals, {max_generations} generations")
        
        # Initialize population
        population = self._initialize_population(population_size)
        best_candidate = max(population, key=lambda x: x.fitness)
        
        for generation in range(max_generations):
            # Evaluate fitness
            for candidate in population:
                candidate.fitness = self._evaluate_fitness(candidate)
            
            # Sort by fitness
            population.sort(key=lambda x: x.fitness, reverse=True)
            
            # Track best
            if population[0].fitness > best_candidate.fitness:
                best_candidate = copy.deepcopy(population[0])
            
            convergence_history.append(best_candidate.fitness)
            
            # Create new population with elitism
            new_population = population[:elite_size]  # Keep elite
            
            # Fill rest with offspring
            while len(new_population) < population_size:
                parent1 = self._tournament_selection(population)
                parent2 = self._tournament_selection(population)
                child = self._crossover(parent1, parent2)
                child = self._mutate(child, mutation_rate)
                new_population.append(child)
            
            population = new_population
        
        # Create result
        motif = self._extract_motif(best_candidate)
        pwm = self.calculate_pwm([motif] if motif else [])
        ic = self.calculate_information_content(pwm)
        runtime = time.time() - start_time
        
        return MotifResult(
            motif=motif,
            score=best_candidate.fitness,
            positions=best_candidate.positions,
            pwm=pwm,
            information_content=ic,
            algorithm="ElitistGA",
            runtime=runtime,
            convergence_history=convergence_history
        )
    
    def _initialize_population(self, size: int) -> List[Candidate]:
        population = []
        for _ in range(size):
            positions = []
            for seq in self.sequences:
                if len(seq) >= self.motif_length:
                    pos = random.randint(0, len(seq) - self.motif_length)
                else:
                    pos = 0
                positions.append(pos)
            
            candidate = Candidate(positions)
            candidate.fitness = self._evaluate_fitness(candidate)
            population.append(candidate)
        
        return population
    
    def _evaluate_fitness(self, candidate: Candidate) -> float:
        motif_instances = []
        for i, pos in enumerate(candidate.positions):
            if pos + self.motif_length <= len(self.sequences[i]):
                motif_instances.append(self.sequences[i][pos:pos + self.motif_length])
        
        if not motif_instances:
            return 0.0
        
        # Calculate consensus
        consensus = self._get_consensus(motif_instances)
        return self.score_motif(consensus, self.sequences)
    
    def _get_consensus(self, instances: List[str]) -> str:
        if not instances:
            return ""
        
        consensus = ""
        for pos in range(self.motif_length):
            counts = {'A': 0, 'T': 0, 'G': 0, 'C': 0}
            for instance in instances:
                if pos < len(instance) and instance[pos] in counts:
                    counts[instance[pos]] += 1
            consensus += max(counts, key=counts.get)
        
        return consensus
    
    def _tournament_selection(self, population: List[Candidate]) -> Candidate:
        tournament_size = 3
        tournament = random.sample(population, min(tournament_size, len(population)))
        return max(tournament, key=lambda x: x.fitness)
    
    def _crossover(self, parent1: Candidate, parent2: Candidate) -> Candidate:
        child_positions = []
        for i in range(len(parent1.positions)):
            if random.random() < 0.5:
                child_positions.append(parent1.positions[i])
            else:
                child_positions.append(parent2.positions[i])
        
        return Candidate(child_positions)
    
    def _mutate(self, candidate: Candidate, mutation_rate: float) -> Candidate:
        for i in range(len(candidate.positions)):
            if random.random() < mutation_rate:
                max_pos = max(0, len(self.sequences[i]) - self.motif_length)
                candidate.positions[i] = random.randint(0, max_pos)
        
        return candidate
    
    def _extract_motif(self, candidate: Candidate) -> str:
        motif_instances = []
        for i, pos in enumerate(candidate.positions):
            if pos + self.motif_length <= len(self.sequences[i]):
                motif_instances.append(self.sequences[i][pos:pos + self.motif_length])
        
        return self._get_consensus(motif_instances)

print("✅ ElitistGA implementation ready")

# Cell 4: InertiaWeightPSO Implementation (Simplified)
class Particle:
    def __init__(self, positions: List[int], velocities: List[float]):
        self.positions = positions
        self.velocities = velocities
        self.current_fitness = 0.0
        self.best_fitness = 0.0
        self.best_positions = positions.copy()

class InertiaWeightPSO(BaseMotifDiscovery):
    """Inertia Weight PSO - Simplified Version"""

    def discover_motifs(self) -> MotifResult:
        start_time = time.time()
        swarm_size = self.config.get('swarm_size', 15)
        max_iterations = self.config.get('max_generations', 30)
        c1 = self.config.get('c1', 2.0)
        c2 = self.config.get('c2', 2.0)
        w_max = self.config.get('w_max', 0.9)
        w_min = self.config.get('w_min', 0.4)
        convergence_history = []

        print(f"   🔄 InertiaWeightPSO: {swarm_size} particles, {max_iterations} iterations")

        # Initialize swarm
        swarm = self._initialize_swarm(swarm_size)
        global_best = self._get_global_best(swarm)

        for iteration in range(max_iterations):
            # Update inertia weight
            w = w_max - (w_max - w_min) * iteration / max_iterations

            for particle in swarm:
                # Update velocity and position
                for i in range(len(particle.positions)):
                    r1, r2 = random.random(), random.random()

                    cognitive = c1 * r1 * (particle.best_positions[i] - particle.positions[i])
                    social = c2 * r2 * (global_best.best_positions[i] - particle.positions[i])

                    particle.velocities[i] = w * particle.velocities[i] + cognitive + social

                    # Update position
                    new_pos = int(particle.positions[i] + particle.velocities[i])
                    max_pos = max(0, len(self.sequences[i]) - self.motif_length)
                    particle.positions[i] = max(0, min(max_pos, new_pos))

                # Evaluate and update personal best
                particle.current_fitness = self._evaluate_particle_fitness(particle)
                if particle.current_fitness > particle.best_fitness:
                    particle.best_fitness = particle.current_fitness
                    particle.best_positions = particle.positions.copy()

            # Update global best
            current_global_best = self._get_global_best(swarm)
            if current_global_best.best_fitness > global_best.best_fitness:
                global_best = copy.deepcopy(current_global_best)

            convergence_history.append(global_best.best_fitness)

        # Create result
        motif = self._extract_motif_from_positions(global_best.best_positions)
        pwm = self.calculate_pwm([motif] if motif else [])
        ic = self.calculate_information_content(pwm)
        runtime = time.time() - start_time

        return MotifResult(
            motif=motif,
            score=global_best.best_fitness,
            positions=global_best.best_positions,
            pwm=pwm,
            information_content=ic,
            algorithm="InertiaWeightPSO",
            runtime=runtime,
            convergence_history=convergence_history
        )

    def _initialize_swarm(self, size: int) -> List[Particle]:
        swarm = []
        for _ in range(size):
            positions = []
            velocities = []

            for seq in self.sequences:
                if len(seq) >= self.motif_length:
                    pos = random.randint(0, len(seq) - self.motif_length)
                else:
                    pos = 0
                positions.append(pos)
                velocities.append(random.uniform(-1, 1))

            particle = Particle(positions, velocities)
            particle.current_fitness = self._evaluate_particle_fitness(particle)
            particle.best_fitness = particle.current_fitness
            particle.best_positions = positions.copy()
            swarm.append(particle)

        return swarm

    def _evaluate_particle_fitness(self, particle: Particle) -> float:
        motif_instances = []
        for i, pos in enumerate(particle.positions):
            if pos + self.motif_length <= len(self.sequences[i]):
                motif_instances.append(self.sequences[i][pos:pos + self.motif_length])

        if not motif_instances:
            return 0.0

        consensus = self._get_consensus(motif_instances)
        return self.score_motif(consensus, self.sequences)

    def _get_consensus(self, instances: List[str]) -> str:
        if not instances:
            return ""

        consensus = ""
        for pos in range(self.motif_length):
            counts = {'A': 0, 'T': 0, 'G': 0, 'C': 0}
            for instance in instances:
                if pos < len(instance) and instance[pos] in counts:
                    counts[instance[pos]] += 1
            consensus += max(counts, key=counts.get)

        return consensus

    def _get_global_best(self, swarm: List[Particle]) -> Particle:
        return max(swarm, key=lambda p: p.best_fitness)

    def _extract_motif_from_positions(self, positions: List[int]) -> str:
        motif_instances = []
        for i, pos in enumerate(positions):
            if pos + self.motif_length <= len(self.sequences[i]):
                motif_instances.append(self.sequences[i][pos:pos + self.motif_length])

        return self._get_consensus(motif_instances)

print("✅ InertiaWeightPSO implementation ready")

# Cell 5: Sequential Hybrid Implementation
class SequentialHybridGAPSO(BaseMotifDiscovery):
    """Sequential GA→PSO Hybrid for APP Gene Analysis"""

    def __init__(self, sequences, motif_length, config):
        super().__init__(sequences, motif_length, config)
        self.hybrid_name = "Sequential_ElitistGA→InertiaWeightPSO"

    def discover_motifs(self) -> MotifResult:
        start_time = time.time()
        convergence_history = []

        print(f"   🔗 Sequential Hybrid: GA exploration → PSO refinement")

        # Phase 1: GA Exploration (60% of computational budget)
        ga_iterations = int(self.config.get('max_generations', 30) * 0.6)
        ga_config = self.config.copy()
        ga_config['max_generations'] = ga_iterations

        print(f"   🧬 Phase 1: ElitistGA exploration ({ga_iterations} iterations)")
        ga_algorithm = ElitistGA(self.sequences, self.motif_length, ga_config)
        ga_result = ga_algorithm.discover_motifs()

        # Phase 2: PSO Refinement (40% of computational budget)
        pso_iterations = int(self.config.get('max_generations', 30) * 0.4)
        pso_config = self.config.copy()
        pso_config['max_generations'] = pso_iterations

        print(f"   🔄 Phase 2: InertiaWeightPSO refinement ({pso_iterations} iterations)")
        pso_algorithm = InertiaWeightPSO(self.sequences, self.motif_length, pso_config)
        pso_result = pso_algorithm.discover_motifs()

        # Combine convergence histories
        convergence_history.extend(ga_result.convergence_history)
        convergence_history.extend(pso_result.convergence_history)

        # Select best result with preference for PSO (refinement phase)
        if pso_result.score >= ga_result.score:
            best_result = pso_result
            improvement = pso_result.score - ga_result.score
            print(f"   🎯 PSO refinement improved score by {improvement:.4f}")
        else:
            best_result = ga_result
            print(f"   🎯 GA exploration achieved better result: {ga_result.score:.4f}")

        runtime = time.time() - start_time

        return MotifResult(
            motif=best_result.motif,
            score=best_result.score,
            positions=best_result.positions,
            pwm=best_result.pwm,
            information_content=best_result.information_content,
            algorithm=self.hybrid_name,
            runtime=runtime,
            convergence_history=convergence_history
        )

print("✅ Sequential Hybrid implementation ready")

# Cell 6: Run Complete APP Pipeline
def run_app_pipeline(sequences, labels):
    """Run complete APP gene motif discovery pipeline"""

    print("\n🚀 Running Complete APP Gene Pipeline")
    print("="*50)

    # Configuration optimized for APP gene analysis
    config = {
        'motif_length': 8,  # Meaningful motif length for regulatory elements
        'max_generations': 20,  # Balanced for speed and quality
        'population_size': 15,
        'swarm_size': 12,
        'mutation_rate': 0.1,
        'c1': 2.0,
        'c2': 2.0,
        'w_max': 0.9,
        'w_min': 0.4
    }

    print(f"📊 Configuration:")
    print(f"   Motif length: {config['motif_length']} bp")
    print(f"   Max generations: {config['max_generations']}")
    print(f"   Population size: {config['population_size']}")

    results = {}

    # 1. ElitistGA
    print(f"\n🧬 Testing ElitistGA...")
    ga_algorithm = ElitistGA(sequences, config['motif_length'], config)
    ga_result = ga_algorithm.discover_motifs()
    results['ElitistGA'] = ga_result

    print(f"✅ ElitistGA Results:")
    print(f"   Motif: {ga_result.motif}")
    print(f"   Score: {ga_result.score:.4f}")
    print(f"   Runtime: {ga_result.runtime:.2f}s")

    # 2. InertiaWeightPSO
    print(f"\n🔄 Testing InertiaWeightPSO...")
    pso_algorithm = InertiaWeightPSO(sequences, config['motif_length'], config)
    pso_result = pso_algorithm.discover_motifs()
    results['InertiaWeightPSO'] = pso_result

    print(f"✅ InertiaWeightPSO Results:")
    print(f"   Motif: {pso_result.motif}")
    print(f"   Score: {pso_result.score:.4f}")
    print(f"   Runtime: {pso_result.runtime:.2f}s")

    # 3. Sequential Hybrid
    print(f"\n🔗 Testing Sequential Hybrid...")
    hybrid_algorithm = SequentialHybridGAPSO(sequences, config['motif_length'], config)
    hybrid_result = hybrid_algorithm.discover_motifs()
    results['Sequential_Hybrid'] = hybrid_result

    print(f"✅ Sequential Hybrid Results:")
    print(f"   Motif: {hybrid_result.motif}")
    print(f"   Score: {hybrid_result.score:.4f}")
    print(f"   Runtime: {hybrid_result.runtime:.2f}s")

    return results, config

# Run the complete pipeline
if len(sequences) > 0:
    print("🧬 Starting Complete APP Gene Pipeline...")

    app_results, app_config = run_app_pipeline(sequences, labels)

    print("\n✅ APP Pipeline completed!")
    print("🎯 Ready for analysis and visualization!")

else:
    print("❌ No sequences loaded. Please check data files.")

print("✅ Complete APP-focused pipeline ready!")
